{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:00:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: <PERSON><PERSON><PERSON> Recently Added Scan","timestamp":"2025-07-30T22:00:00.115Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"70be880a-b2c3-43d2-aec4-b364dc15ce4c","timestamp":"2025-07-30T22:00:00.115Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T22:00:00.172Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T22:00:00.239Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T22:00:00.242Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:01:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:02:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:03:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:04:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:05:00.025Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-30T22:05:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"557a6bfd-56ab-40bf-87b0-5e2f2849a8bb","timestamp":"2025-07-30T22:05:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T22:05:00.038Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T22:05:00.051Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T22:05:00.054Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:06:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:07:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:08:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:09:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:10:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-30T22:10:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"52ef6310-1301-400b-a52f-b0fa51414b0c","timestamp":"2025-07-30T22:10:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T22:10:00.016Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T22:10:00.064Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T22:10:00.070Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:11:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:12:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:13:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:14:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:15:00.004Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-30T22:15:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"610cc8aa-d58c-4103-b7f1-6407621871d8","timestamp":"2025-07-30T22:15:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T22:15:00.028Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T22:15:00.048Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T22:15:00.050Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:16:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:17:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:18:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:19:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:20:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-30T22:20:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"f5f85d75-e7fd-4721-993a-d43ccbe74228","timestamp":"2025-07-30T22:20:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T22:20:00.014Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T22:20:00.018Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T22:20:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:21:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:22:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:23:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:24:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:25:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-30T22:25:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"deaa6706-99ec-48d1-870a-eee1be766404","timestamp":"2025-07-30T22:25:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T22:25:00.011Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T22:25:00.046Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T22:25:00.047Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:26:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:27:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:28:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:29:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:30:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-30T22:30:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"7afff247-0bd7-49fa-8685-e92c46a13d9f","timestamp":"2025-07-30T22:30:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T22:30:00.032Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T22:30:00.067Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T22:30:00.067Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:31:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:32:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:33:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:34:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:35:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-30T22:35:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"b51dfcf1-f25b-45d0-8b46-70dceb5f55e1","timestamp":"2025-07-30T22:35:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T22:35:00.029Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T22:35:00.067Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T22:35:00.067Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:36:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:37:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:38:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:39:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:40:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-30T22:40:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"eef094c1-ccf1-4d66-9f45-4b0007eae9fd","timestamp":"2025-07-30T22:40:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T22:40:00.032Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T22:40:00.069Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T22:40:00.070Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:41:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:42:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:43:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:44:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:45:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-30T22:45:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"7f49b7af-0c24-4c3d-a97b-e6d1a2c5b7cd","timestamp":"2025-07-30T22:45:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T22:45:00.034Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T22:45:00.080Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T22:45:00.080Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:46:00.032Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:47:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:48:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:49:00.031Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:50:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-30T22:50:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"498956dd-fcca-42f4-a096-b0f55d0cd5a0","timestamp":"2025-07-30T22:50:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T22:50:00.032Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T22:50:00.079Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T22:50:00.079Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:51:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:52:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:53:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:54:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:55:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-30T22:55:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"43eccb3a-d008-49dc-9b3f-bb1784f91e02","timestamp":"2025-07-30T22:55:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T22:55:00.028Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T22:55:00.061Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T22:55:00.061Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:56:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:57:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:58:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T22:59:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:00:00.019Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-30T23:00:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"9874c235-ff71-4ddc-9ba2-43a43f0c9c2a","timestamp":"2025-07-30T23:00:00.031Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Download Sync Reset","timestamp":"2025-07-30T23:00:00.075Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T23:00:00.082Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T23:00:00.130Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T23:00:00.133Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:01:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:02:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:03:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:04:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:05:00.022Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-30T23:05:00.028Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"54fac2fa-8233-4925-8d85-5e78afa25659","timestamp":"2025-07-30T23:05:00.029Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T23:05:00.045Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T23:05:00.109Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T23:05:00.110Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:06:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:07:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:08:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:09:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:10:00.022Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-30T23:10:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"20935934-9f7a-4974-b51d-886e9bcdf31d","timestamp":"2025-07-30T23:10:00.029Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T23:10:00.042Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T23:10:00.077Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T23:10:00.077Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:11:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:12:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:13:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:14:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:15:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-30T23:15:00.033Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"96c675fa-6d33-45f2-a8f1-2d8bd5263dd9","timestamp":"2025-07-30T23:15:00.033Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T23:15:00.044Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T23:15:00.095Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T23:15:00.095Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:16:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:17:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:18:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:19:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:20:00.019Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-30T23:20:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ee49d634-ff4f-42ee-8769-816bb1e0f282","timestamp":"2025-07-30T23:20:00.029Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T23:20:00.062Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T23:20:00.119Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T23:20:00.121Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:21:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:22:00.000Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:23:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:24:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:25:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-30T23:25:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8538801f-1c84-43bb-8a8a-2c1fed8a4685","timestamp":"2025-07-30T23:25:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T23:25:00.022Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T23:25:00.075Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T23:25:00.079Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:26:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:27:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:28:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:29:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:30:00.024Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-30T23:30:00.028Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"2cbdb8d8-c3fa-4e2b-ba78-ff535b594e75","timestamp":"2025-07-30T23:30:00.032Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T23:30:00.069Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T23:30:00.112Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T23:30:00.115Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:31:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:32:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:33:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:34:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:35:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-30T23:35:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"47734336-f9af-4f75-9770-a11f5f26b6c2","timestamp":"2025-07-30T23:35:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T23:35:00.016Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T23:35:00.099Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T23:35:00.102Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:36:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:37:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:38:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:39:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:40:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-30T23:40:00.031Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"0f541581-d2e3-4712-8cde-81d5bd84df03","timestamp":"2025-07-30T23:40:00.031Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T23:40:00.053Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T23:40:00.120Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T23:40:00.121Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:41:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:42:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:43:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:44:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:45:00.022Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-30T23:45:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8b704c24-56f3-4564-b727-f78a03549e99","timestamp":"2025-07-30T23:45:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T23:45:00.073Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T23:45:00.156Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T23:45:00.157Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:46:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:47:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:48:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:49:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:50:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-30T23:50:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"6d7efd47-4c1d-46d0-aba0-adae0d15ab21","timestamp":"2025-07-30T23:50:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T23:50:00.018Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T23:50:00.055Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T23:50:00.056Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:51:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:52:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:53:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:54:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:55:00.023Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-30T23:55:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ed63087c-1b66-472b-a1d6-a678aa4ed7ed","timestamp":"2025-07-30T23:55:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-30T23:55:00.042Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-30T23:55:00.072Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-30T23:55:00.076Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:56:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:57:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:58:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-30T23:59:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:00:00.022Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T00:00:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d3e5a625-7bba-4d59-a41e-45b30fd762bc","timestamp":"2025-07-31T00:00:00.031Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T00:00:00.045Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T00:00:00.086Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T00:00:00.088Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:01:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:02:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:03:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:04:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:05:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T00:05:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"0927ee97-bc5a-4dc2-8ab0-2289dea4cd6c","timestamp":"2025-07-31T00:05:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T00:05:00.013Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T00:05:00.049Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T00:05:00.050Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:06:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:07:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:08:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:09:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:10:00.023Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T00:10:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"0c39f9c7-7f51-4ec5-be04-266d1b9a0341","timestamp":"2025-07-31T00:10:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T00:10:00.032Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T00:10:00.063Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T00:10:00.063Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:11:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:12:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:13:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:14:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:15:00.023Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T00:15:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"06fc991c-269d-439e-839f-480d03796ab4","timestamp":"2025-07-31T00:15:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T00:15:00.031Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T00:15:00.036Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T00:15:00.037Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:16:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:17:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:18:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:19:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:20:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T00:20:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8d411bcf-01cd-44a9-aa81-4d7d644b288c","timestamp":"2025-07-31T00:20:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T00:20:00.058Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T00:20:00.117Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T00:20:00.118Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:21:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:22:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:23:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:24:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:25:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T00:25:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"2130e7e5-9257-42a2-813b-18c2aba96087","timestamp":"2025-07-31T00:25:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T00:25:00.027Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T00:25:00.063Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T00:25:00.064Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:26:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:27:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:28:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:29:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:30:00.022Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T00:30:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"7e123ffd-cdbf-49e7-999f-b04a45427fd7","timestamp":"2025-07-31T00:30:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T00:30:00.030Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T00:30:00.066Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T00:30:00.067Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:31:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:32:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:33:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:34:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:35:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T00:35:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"a01ead1c-3a26-4792-a7cf-e666de315673","timestamp":"2025-07-31T00:35:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T00:35:00.047Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T00:35:00.107Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T00:35:00.109Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:36:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:37:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:38:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:39:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:40:00.019Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T00:40:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"68636a1f-acb7-4e60-9c80-1d54549a9875","timestamp":"2025-07-31T00:40:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T00:40:00.039Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T00:40:00.083Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T00:40:00.083Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:41:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:42:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:43:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:44:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:45:00.017Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T00:45:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"59113f07-8828-436f-a196-76584e36e676","timestamp":"2025-07-31T00:45:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T00:45:00.031Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T00:45:00.098Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T00:45:00.099Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:46:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:47:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:48:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:49:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:50:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T00:50:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"42f89923-07bf-4be4-b950-95b01563fa10","timestamp":"2025-07-31T00:50:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T00:50:00.043Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T00:50:00.110Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T00:50:00.111Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:51:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:52:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:53:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:54:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:55:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T00:55:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"7066f9bb-7549-4dc9-b57a-b825ea5ef18e","timestamp":"2025-07-31T00:55:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T00:55:00.036Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T00:55:00.079Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T00:55:00.081Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:56:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:57:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:58:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T00:59:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:00:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T01:00:00.033Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"b665ff06-6d1e-473f-9acb-742a370ce13a","timestamp":"2025-07-31T01:00:00.037Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Full Scan","timestamp":"2025-07-31T01:00:00.046Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"70056763-7feb-4534-a83e-1109d3645945","timestamp":"2025-07-31T01:00:00.046Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T01:00:00.074Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process library: Películas","timestamp":"2025-07-31T01:00:00.088Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T01:00:00.205Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T01:00:00.207Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T01:00:00.211Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T01:00:00.213Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:01:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:02:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:03:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:04:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:05:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T01:05:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"e6145667-8abf-4838-a1fa-2f86c913d189","timestamp":"2025-07-31T01:05:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T01:05:00.016Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T01:05:00.050Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T01:05:00.053Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:06:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:07:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:08:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:09:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:10:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T01:10:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"3d3051db-656d-4cb9-9d3c-30bbbe0fb43a","timestamp":"2025-07-31T01:10:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T01:10:00.015Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T01:10:00.078Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T01:10:00.078Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:11:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:12:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:13:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:14:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:15:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T01:15:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"82b38e62-3f79-47c8-86ae-0a58f9c9ee31","timestamp":"2025-07-31T01:15:00.028Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T01:15:00.043Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T01:15:00.096Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T01:15:00.096Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:16:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:17:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:18:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:19:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:20:00.022Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T01:20:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"7165e12c-bb5f-4103-b1ef-43afaec04cd8","timestamp":"2025-07-31T01:20:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T01:20:00.045Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T01:20:00.104Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T01:20:00.105Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:21:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:22:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:23:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:24:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:25:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T01:25:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"437e83d8-8b40-4821-bdf6-64abad94cffc","timestamp":"2025-07-31T01:25:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T01:25:00.037Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T01:25:00.090Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T01:25:00.091Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:26:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:27:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:28:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:29:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:30:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T01:30:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"f3d3c2e9-be4d-4608-8912-b72dd4ee913c","timestamp":"2025-07-31T01:30:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T01:30:00.012Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T01:30:00.047Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T01:30:00.050Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:31:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:32:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:33:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:34:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:35:00.005Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T01:35:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"1250feb8-7ec0-4261-8376-7bdafc155749","timestamp":"2025-07-31T01:35:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T01:35:00.025Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T01:35:00.077Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T01:35:00.080Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:36:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:37:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:38:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:39:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:40:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T01:40:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"c7b355e5-d283-4b09-85db-b8d1a429a416","timestamp":"2025-07-31T01:40:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T01:40:00.014Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T01:40:00.050Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T01:40:00.050Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:41:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:42:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:43:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:44:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:45:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T01:45:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"356b123f-822a-409f-b062-895c76da7dfb","timestamp":"2025-07-31T01:45:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T01:45:00.021Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T01:45:00.079Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T01:45:00.079Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:46:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:47:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:48:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:49:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:50:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T01:50:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"1ec9920b-eefc-48c3-a75b-80895e5237d4","timestamp":"2025-07-31T01:50:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T01:50:00.032Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T01:50:00.087Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T01:50:00.088Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:51:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:52:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:53:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:54:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:55:00.016Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T01:55:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"0a5c8b23-fd7d-419c-831d-c3dfa8a7f7ed","timestamp":"2025-07-31T01:55:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T01:55:00.069Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T01:55:00.118Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T01:55:00.119Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:56:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:57:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:58:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T01:59:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:00:00.019Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T02:00:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"98c52237-e225-417c-9a18-bc019d0bffe2","timestamp":"2025-07-31T02:00:00.023Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Radarr Scan","timestamp":"2025-07-31T02:00:00.032Z"}
{"label":"Radarr Scan","level":"info","message":"Scan starting","sessionId":"eca1d286-f771-43ca-9728-1aa12ae7ef2d","timestamp":"2025-07-31T02:00:00.032Z"}
{"label":"Radarr Scan","level":"info","message":"Beginning to process Radarr server: Radarr","timestamp":"2025-07-31T02:00:00.032Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T02:00:00.047Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T02:00:00.095Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T02:00:00.097Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Un lugar tranquilo: Día uno","timestamp":"2025-07-31T02:00:00.866Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Malditos bastardos","timestamp":"2025-07-31T02:00:00.869Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Ocean's 8","timestamp":"2025-07-31T02:00:00.873Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Ocean's Eleven. Hagan juego","timestamp":"2025-07-31T02:00:00.873Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Ocean's Thirteen","timestamp":"2025-07-31T02:00:00.874Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Ocean's Twelve","timestamp":"2025-07-31T02:00:00.874Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Oppenheimer","timestamp":"2025-07-31T02:00:00.877Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Capitán América: Brave New World","timestamp":"2025-07-31T02:00:00.877Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Shutter Island","timestamp":"2025-07-31T02:00:00.878Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for El contable 2","timestamp":"2025-07-31T02:00:00.878Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Las crónicas de Narnia: El príncipe Caspian","timestamp":"2025-07-31T02:00:00.879Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Las crónicas de Narnia: El león, la bruja y el armario","timestamp":"2025-07-31T02:00:00.880Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Los odiosos ocho","timestamp":"2025-07-31T02:00:00.881Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for El señor de los anillos: La comunidad del anillo","timestamp":"2025-07-31T02:00:00.881Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Until Dawn","timestamp":"2025-07-31T02:00:00.883Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Los pecadores","timestamp":"2025-07-31T02:00:00.884Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for El mundo perdido: Jurassic Park","timestamp":"2025-07-31T02:00:00.885Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Kung Fu Panda","timestamp":"2025-07-31T02:00:00.888Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Kung Fu Panda 2","timestamp":"2025-07-31T02:00:00.888Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Kung Fu Panda 3","timestamp":"2025-07-31T02:00:00.889Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Gru 4. Mi villano favorito","timestamp":"2025-07-31T02:00:00.895Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Interstellar","timestamp":"2025-07-31T02:00:00.896Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Las crónicas de Narnia: La travesía del viajero del alba","timestamp":"2025-07-31T02:00:00.897Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Kung Fu Panda 4","timestamp":"2025-07-31T02:00:00.898Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Prometheus","timestamp":"2025-07-31T02:00:00.898Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Destino final: Lazos de sangre","timestamp":"2025-07-31T02:00:00.898Z"}
{"label":"Radarr Scan","level":"debug","message":"Title already exists and no changes detected for Una película de Minecraft","timestamp":"2025-07-31T02:00:00.899Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Babylon exists. Changes were detected and the title will be updated.","timestamp":"2025-07-31T02:00:00.935Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Destino final exists. Changes were detected and the title will be updated.","timestamp":"2025-07-31T02:00:00.936Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Cómo entrenar a tu dragón exists. Changes were detected and the title will be updated.","timestamp":"2025-07-31T02:00:00.936Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Cómo entrenar a tu dragón 3 exists. Changes were detected and the title will be updated.","timestamp":"2025-07-31T02:00:00.937Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Jurassic Park (Parque Jurásico) exists. Changes were detected and the title will be updated.","timestamp":"2025-07-31T02:00:00.937Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Django desencadenado exists. Changes were detected and the title will be updated.","timestamp":"2025-07-31T02:00:00.941Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Destino final 5 exists. Changes were detected and the title will be updated.","timestamp":"2025-07-31T02:00:00.942Z"}
{"label":"Radarr Scan","level":"info","message":"Media for El renacido exists. Changes were detected and the title will be updated.","timestamp":"2025-07-31T02:00:00.946Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Cómo entrenar a tu dragón 2 exists. Changes were detected and the title will be updated.","timestamp":"2025-07-31T02:00:00.950Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Destino final 3 exists. Changes were detected and the title will be updated.","timestamp":"2025-07-31T02:00:00.951Z"}
{"label":"Radarr Scan","level":"info","message":"Media for American History X exists. Changes were detected and the title will be updated.","timestamp":"2025-07-31T02:00:00.951Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Los renglones torcidos de Dios exists. Changes were detected and the title will be updated.","timestamp":"2025-07-31T02:00:00.952Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Destino final 4 exists. Changes were detected and the title will be updated.","timestamp":"2025-07-31T02:00:00.952Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Náufrago exists. Changes were detected and the title will be updated.","timestamp":"2025-07-31T02:00:00.955Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Vengadores: Infinity War exists. Changes were detected and the title will be updated.","timestamp":"2025-07-31T02:00:00.958Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Destino final 2 exists. Changes were detected and the title will be updated.","timestamp":"2025-07-31T02:00:00.958Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Jungla de cristal exists. Changes were detected and the title will be updated.","timestamp":"2025-07-31T02:00:00.959Z"}
{"label":"Radarr Scan","level":"info","message":"Media for Cómo entrenar a tu dragón exists. Changes were detected and the title will be updated.","timestamp":"2025-07-31T02:00:00.959Z"}
{"label":"Radarr Scan","level":"info","message":"Media for John Wick 4 exists. Changes were detected and the title will be updated.","timestamp":"2025-07-31T02:00:00.959Z"}
{"label":"Radarr Scan","level":"info","message":"Radarr scan complete","timestamp":"2025-07-31T02:00:04.964Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:01:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:02:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:03:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:04:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:05:00.000Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T02:05:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"e8eb97de-5d71-405f-bb2c-9a75c301c9c6","timestamp":"2025-07-31T02:05:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T02:05:00.017Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T02:05:00.059Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T02:05:00.061Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:06:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:07:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:08:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:09:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:10:00.024Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T02:10:00.028Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"3b136cfc-16b3-46c6-b0e7-4767651776dd","timestamp":"2025-07-31T02:10:00.029Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T02:10:00.038Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T02:10:00.087Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T02:10:00.091Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:11:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:12:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:13:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:14:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:15:00.024Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T02:15:00.035Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"aa0910b3-38c7-4f6d-ab9d-5c93f058b483","timestamp":"2025-07-31T02:15:00.036Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T02:15:00.050Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T02:15:00.102Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T02:15:00.103Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:16:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:17:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:18:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:19:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:20:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T02:20:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"23871171-6573-4034-8a19-848b4721834a","timestamp":"2025-07-31T02:20:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T02:20:00.044Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T02:20:00.098Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T02:20:00.106Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:21:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:22:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:23:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:24:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:25:00.019Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T02:25:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"42558695-df19-49fb-8751-f2a148ee277b","timestamp":"2025-07-31T02:25:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T02:25:00.033Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T02:25:00.040Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T02:25:00.040Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:26:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:27:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:28:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:29:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:30:00.012Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T02:30:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"7e033b8b-a3a7-410f-bfe3-c3e5987eda64","timestamp":"2025-07-31T02:30:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Sonarr Scan","timestamp":"2025-07-31T02:30:00.020Z"}
{"label":"Sonarr Scan","level":"info","message":"Scan starting","sessionId":"cff8f61d-d5a6-457e-91a2-bc899736fb68","timestamp":"2025-07-31T02:30:00.020Z"}
{"label":"Sonarr Scan","level":"info","message":"Beginning to process Sonarr server: Sonarr","timestamp":"2025-07-31T02:30:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T02:30:00.026Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T02:30:00.058Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T02:30:00.058Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Prison Break","timestamp":"2025-07-31T02:30:01.174Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Dark","timestamp":"2025-07-31T02:30:01.321Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Squid Game","timestamp":"2025-07-31T02:30:01.342Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Stranger Things","timestamp":"2025-07-31T02:30:01.347Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Peaky Blinders","timestamp":"2025-07-31T02:30:01.355Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Better Call Saul","timestamp":"2025-07-31T02:30:01.363Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Dragon Ball","timestamp":"2025-07-31T02:30:01.376Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Rick and Morty","timestamp":"2025-07-31T02:30:01.377Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Breaking Bad","timestamp":"2025-07-31T02:30:01.390Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: The Big Bang Theory","timestamp":"2025-07-31T02:30:01.403Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: The Boys","timestamp":"2025-07-31T02:30:01.409Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: House of the Dragon","timestamp":"2025-07-31T02:30:01.418Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Friends","timestamp":"2025-07-31T02:30:01.424Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Dexter","timestamp":"2025-07-31T02:30:01.426Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Outlander","timestamp":"2025-07-31T02:30:01.428Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Game of Thrones","timestamp":"2025-07-31T02:30:01.468Z"}
{"label":"Sonarr Scan","level":"debug","message":"Updating existing title: Black Mirror","timestamp":"2025-07-31T02:30:01.470Z"}
{"label":"Sonarr Scan","level":"info","message":"Sonarr scan complete","timestamp":"2025-07-31T02:30:05.471Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:31:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:32:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:33:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:34:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:35:00.016Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T02:35:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"5f7b7cc7-acfd-403a-9710-3f9d36b99024","timestamp":"2025-07-31T02:35:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T02:35:00.025Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T02:35:00.057Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T02:35:00.057Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:36:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:37:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:38:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:39:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:40:00.014Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T02:40:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"3946d38e-3a10-4a7a-a7ed-7ca8bf2c777f","timestamp":"2025-07-31T02:40:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T02:40:00.024Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T02:40:00.063Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T02:40:00.066Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:41:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:42:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:43:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:44:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:45:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T02:45:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"94222a28-3857-4022-bb13-e65332554309","timestamp":"2025-07-31T02:45:00.004Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T02:45:00.010Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T02:45:00.042Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T02:45:00.043Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:46:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:47:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:48:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:49:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:50:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T02:50:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"661bf19b-f4ef-467e-a256-8204e841ff40","timestamp":"2025-07-31T02:50:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T02:50:00.023Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T02:50:00.053Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T02:50:00.054Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:51:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:52:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:53:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:54:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:55:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T02:55:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ee8f05a8-2d44-4166-8349-f527320aff6b","timestamp":"2025-07-31T02:55:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T02:55:00.011Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T02:55:00.039Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T02:55:00.041Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:56:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:57:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:58:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T02:59:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:00:00.011Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T03:00:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"c4f5b410-834c-4514-9691-cb1f81a3d30a","timestamp":"2025-07-31T03:00:00.013Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Image Cache Cleanup","timestamp":"2025-07-31T03:00:00.017Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Media Availability Sync","timestamp":"2025-07-31T03:00:00.022Z"}
{"label":"Availability Sync","level":"info","message":"Starting availability sync...","timestamp":"2025-07-31T03:00:00.024Z"}
{"label":"Image Cache","level":"error","message":"ENOENT: no such file or directory, scandir '/app/config/cache/images/tmdb'","timestamp":"2025-07-31T03:00:00.026Z"}
{"label":"Image Cache","level":"info","message":"Cleared 0 stale image(s) from cache 'tmdb'","timestamp":"2025-07-31T03:00:00.026Z"}
{"label":"Image Cache","level":"info","message":"Cleared 0 stale image(s) from cache 'avatar'","timestamp":"2025-07-31T03:00:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T03:00:00.031Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T03:00:00.063Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T03:00:00.064Z"}
{"error":"apiError","errorMessage":"INVALID_AUTH_TOKEN","label":"AvailabilitySync","level":"error","message":"Sync interrupted.","status":401,"timestamp":"2025-07-31T03:00:00.066Z"}
{"label":"Availability Sync","level":"info","message":"Availability sync complete.","timestamp":"2025-07-31T03:00:00.066Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:01:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:02:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:03:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:04:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:05:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T03:05:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"bce36163-9b64-4f14-8487-71f99104ebfc","timestamp":"2025-07-31T03:05:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T03:05:00.019Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T03:05:00.054Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T03:05:00.056Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:06:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:07:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:08:00.028Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:09:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:10:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T03:10:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"9706c5c6-a43a-41ae-96c5-75f130701184","timestamp":"2025-07-31T03:10:00.033Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T03:10:00.045Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T03:10:00.092Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T03:10:00.094Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:11:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:12:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:13:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:14:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:15:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T03:15:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"4c50ba3f-a777-4b7d-944f-827d7d2b1476","timestamp":"2025-07-31T03:15:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T03:15:00.015Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T03:15:00.081Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T03:15:00.083Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:16:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:17:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:18:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:19:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:20:00.008Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T03:20:00.034Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"65565cbe-6d17-46dd-964d-a0dcedbfe8ec","timestamp":"2025-07-31T03:20:00.035Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T03:20:00.096Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T03:20:00.169Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T03:20:00.169Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:21:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:22:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:23:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:24:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:25:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T03:25:00.041Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ece6ac94-fd05-45e4-93a7-ee747019932b","timestamp":"2025-07-31T03:25:00.043Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T03:25:00.055Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T03:25:00.092Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T03:25:00.093Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:26:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:27:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:28:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:29:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:30:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T03:30:00.042Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"5fd55c23-bc83-4542-9212-2a400b70afc5","timestamp":"2025-07-31T03:30:00.042Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T03:30:00.087Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T03:30:00.141Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T03:30:00.142Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:31:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:32:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:33:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:34:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:35:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T03:35:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"c5d88eb2-20bb-4afd-8d76-011663488bb6","timestamp":"2025-07-31T03:35:00.008Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T03:35:00.026Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T03:35:00.055Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T03:35:00.057Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:36:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:37:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:38:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:39:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:40:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T03:40:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"5e34488b-e73b-46a1-812d-92b787d8ff4e","timestamp":"2025-07-31T03:40:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T03:40:00.038Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T03:40:00.258Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T03:40:00.258Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:41:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:42:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:43:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:44:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:45:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T03:45:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"0986b693-f744-4b79-bcbc-9dc62cbe63c3","timestamp":"2025-07-31T03:45:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T03:45:00.030Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T03:45:00.071Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T03:45:00.071Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:46:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:47:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:48:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:49:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:50:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T03:50:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8c7c325b-f923-4a82-9b88-ab227bceed1e","timestamp":"2025-07-31T03:50:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T03:50:00.015Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T03:50:00.045Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T03:50:00.045Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:51:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:52:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:53:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:54:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:55:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T03:55:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"9412f114-e88e-46c2-b654-8c490f2c6acd","timestamp":"2025-07-31T03:55:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T03:55:00.028Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T03:55:00.058Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T03:55:00.058Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:56:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:57:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:58:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T03:59:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:00:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T04:00:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"aac69d0e-eeec-4c82-81fc-a3e28b853444","timestamp":"2025-07-31T04:00:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T04:00:00.035Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T04:00:00.108Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T04:00:00.109Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:01:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:02:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:03:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:04:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:05:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T04:05:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"acabcfd4-7df4-46e9-9d75-172d7c092145","timestamp":"2025-07-31T04:05:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T04:05:00.037Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T04:05:00.084Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T04:05:00.096Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:06:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:07:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:08:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:09:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:10:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T04:10:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"b2a9a780-1b1b-4541-b919-ee7da84da42a","timestamp":"2025-07-31T04:10:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T04:10:00.018Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T04:10:00.059Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T04:10:00.061Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:11:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:12:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:13:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:14:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:15:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T04:15:00.029Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"903ecf6b-6573-472e-bbe1-dcb9884db59c","timestamp":"2025-07-31T04:15:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T04:15:00.040Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T04:15:00.082Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T04:15:00.084Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:16:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:17:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:18:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:19:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:20:00.019Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T04:20:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"6a3edc51-a2af-4e2c-ba17-ef4602dbc1b1","timestamp":"2025-07-31T04:20:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T04:20:00.041Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T04:20:00.095Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T04:20:00.095Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:21:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:22:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:23:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:24:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:25:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T04:25:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d55c733f-05f6-4d57-93ce-af3936315e34","timestamp":"2025-07-31T04:25:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T04:25:00.030Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T04:25:00.077Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T04:25:00.094Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:26:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:27:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:28:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:29:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:30:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T04:30:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"9b4413aa-23cc-4dfa-9a35-b495e731caea","timestamp":"2025-07-31T04:30:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T04:30:00.028Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T04:30:00.061Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T04:30:00.062Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:31:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:32:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:33:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:34:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:35:00.019Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T04:35:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"75715733-9f4e-47b8-9b86-b0fb9ead955a","timestamp":"2025-07-31T04:35:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T04:35:00.028Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T04:35:00.059Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T04:35:00.060Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:36:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:37:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:38:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:39:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:40:00.013Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T04:40:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"83634712-5a20-458f-b27c-1a804998a715","timestamp":"2025-07-31T04:40:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T04:40:00.024Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T04:40:00.067Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T04:40:00.071Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:41:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:42:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:43:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:44:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:45:00.023Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T04:45:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"6f7e0b89-9c81-44f8-b716-5d1af6077415","timestamp":"2025-07-31T04:45:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T04:45:00.034Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T04:45:00.039Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T04:45:00.039Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:46:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:47:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:48:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:49:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:50:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T04:50:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"a0810d10-deb3-4fe8-928d-d2f8b7972f84","timestamp":"2025-07-31T04:50:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T04:50:00.031Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T04:50:00.036Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T04:50:00.036Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:51:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:52:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:53:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:54:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:55:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T04:55:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"04d50b1d-134b-46a9-ad42-f749a5483f46","timestamp":"2025-07-31T04:55:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T04:55:00.035Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T04:55:00.085Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T04:55:00.089Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:56:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:57:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:58:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T04:59:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:00:00.009Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T05:00:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"9145eafc-8909-4702-8e77-86088dc9ea09","timestamp":"2025-07-31T05:00:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T05:00:00.017Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T05:00:00.052Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T05:00:00.052Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:01:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:02:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:03:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:04:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:05:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T05:05:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d2f57b1c-7ba9-4f52-a668-334d3ec0d004","timestamp":"2025-07-31T05:05:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T05:05:00.026Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T05:05:00.057Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T05:05:00.058Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:06:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:07:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:08:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:09:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:10:00.016Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T05:10:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"00d95b3b-5a3a-4fb5-8303-a4de91739cfc","timestamp":"2025-07-31T05:10:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T05:10:00.028Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T05:10:00.069Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T05:10:00.069Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:11:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:12:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:13:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:14:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:15:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T05:15:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"58968484-c1d1-4b07-9d77-060613d3bded","timestamp":"2025-07-31T05:15:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T05:15:00.034Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T05:15:00.079Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T05:15:00.080Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:16:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:17:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:18:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:19:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:20:00.023Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T05:20:00.031Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"119f0877-cf07-4fb8-8864-81cae68eeaec","timestamp":"2025-07-31T05:20:00.031Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T05:20:00.049Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T05:20:00.130Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T05:20:00.137Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:21:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:22:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:23:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:24:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:25:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T05:25:00.038Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"7e992d13-7fad-4dc2-a960-e363d9b5113c","timestamp":"2025-07-31T05:25:00.039Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T05:25:00.066Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T05:25:00.227Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T05:25:00.228Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:26:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:27:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:28:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:29:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:30:00.016Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T05:30:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"878e9e2c-c04b-41b3-955a-400cc166d2eb","timestamp":"2025-07-31T05:30:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T05:30:00.040Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T05:30:00.119Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T05:30:00.120Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:31:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:32:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:33:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:34:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:35:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T05:35:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"b0d0e931-2923-4c25-ac23-958a532948fd","timestamp":"2025-07-31T05:35:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T05:35:00.038Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T05:35:00.112Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T05:35:00.114Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:36:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:37:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:38:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:39:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:40:00.023Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T05:40:00.035Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"7c07c8eb-c35d-4928-9866-b765f8d63aaa","timestamp":"2025-07-31T05:40:00.036Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T05:40:00.078Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T05:40:00.133Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T05:40:00.136Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:41:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:42:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:43:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:44:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:45:00.024Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T05:45:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"c5d30d96-c840-4a88-b48d-1f8a1339095e","timestamp":"2025-07-31T05:45:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T05:45:00.110Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T05:45:00.228Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T05:45:00.235Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:46:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:47:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:48:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:49:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:50:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T05:50:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"7e54ecbb-8865-48bf-bf46-dd6f6cac26bc","timestamp":"2025-07-31T05:50:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T05:50:00.031Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T05:50:00.090Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T05:50:00.092Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:51:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:52:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:53:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:54:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:55:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T05:55:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"173fbe59-be4a-49bd-a15b-570c35ee4353","timestamp":"2025-07-31T05:55:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T05:55:00.038Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T05:55:00.100Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T05:55:00.101Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:56:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:57:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:58:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T05:59:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:00:00.022Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T06:00:00.028Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"6c763c43-f8ac-4c75-9eb7-ec3eb7e1eb51","timestamp":"2025-07-31T06:00:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T06:00:00.044Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T06:00:00.101Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T06:00:00.101Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:01:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:02:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:03:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:04:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:05:00.005Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T06:05:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"1028b73f-7878-4929-b00e-ccec569e7241","timestamp":"2025-07-31T06:05:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T06:05:00.022Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T06:05:00.061Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T06:05:00.063Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:06:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:07:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:08:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:09:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:10:00.019Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T06:10:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"2e125b97-a9be-4b24-9771-79d00bdb320f","timestamp":"2025-07-31T06:10:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T06:10:00.027Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T06:10:00.056Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T06:10:00.057Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:11:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:12:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:13:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:14:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:15:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T06:15:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"f0875275-c2b5-4117-a9d7-95978a267e2e","timestamp":"2025-07-31T06:15:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T06:15:00.019Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T06:15:00.062Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T06:15:00.064Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:16:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:17:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:18:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:19:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:20:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T06:20:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"35279d8c-4f11-42fc-80aa-8b60761528a2","timestamp":"2025-07-31T06:20:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T06:20:00.026Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T06:20:00.074Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T06:20:00.074Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:21:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:22:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:23:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:24:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:25:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T06:25:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"348f8273-097e-42e4-8a9a-72e36bb25753","timestamp":"2025-07-31T06:25:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T06:25:00.030Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T06:25:00.056Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T06:25:00.056Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:26:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:27:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:28:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:29:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:30:00.004Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T06:30:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"c19381a6-3d97-4f1e-9a02-c108c5e90d1d","timestamp":"2025-07-31T06:30:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T06:30:00.031Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T06:30:00.068Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T06:30:00.068Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:31:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:32:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:33:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:34:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:35:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T06:35:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"17474b1f-fcd0-41a8-b059-f704811579e0","timestamp":"2025-07-31T06:35:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T06:35:00.038Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T06:35:00.086Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T06:35:00.086Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:36:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:37:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:38:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:39:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:40:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T06:40:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"a2128a98-4243-4919-9465-2a45f67d702f","timestamp":"2025-07-31T06:40:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T06:40:00.017Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T06:40:00.052Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T06:40:00.057Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:41:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:42:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:43:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:44:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:45:00.019Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T06:45:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"9e4fc2a6-1d25-4769-a199-cdbd3c357606","timestamp":"2025-07-31T06:45:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T06:45:00.033Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T06:45:00.076Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T06:45:00.079Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:46:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:47:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:48:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:49:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:50:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T06:50:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"c998cf6c-ab4b-4e0a-9f2f-5d601d11a101","timestamp":"2025-07-31T06:50:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T06:50:00.014Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T06:50:00.050Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T06:50:00.054Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:51:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:52:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:53:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:54:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:55:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T06:55:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"4359b6a4-909c-43bd-a996-32e1a0bbcf13","timestamp":"2025-07-31T06:55:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T06:55:00.018Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T06:55:00.081Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T06:55:00.083Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:56:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:57:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:58:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T06:59:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:00:00.019Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T07:00:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"2a1ff30d-6087-45ce-a9ca-5a35b06ac675","timestamp":"2025-07-31T07:00:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T07:00:00.036Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T07:00:00.084Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T07:00:00.084Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:01:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:02:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:03:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:04:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:05:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T07:05:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"674c341b-e08e-4ed5-9c96-8b9f25aee0ce","timestamp":"2025-07-31T07:05:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T07:05:00.010Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T07:05:00.045Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T07:05:00.045Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:06:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:07:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:08:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:09:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:10:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T07:10:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"85cf2957-ac91-4158-9109-da25690312b1","timestamp":"2025-07-31T07:10:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T07:10:00.035Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T07:10:00.096Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T07:10:00.098Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:11:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:12:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:13:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:14:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:15:00.017Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T07:15:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"2d9250e8-8987-4927-9e3b-534143d5b9a9","timestamp":"2025-07-31T07:15:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T07:15:00.043Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T07:15:00.057Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T07:15:00.061Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:16:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:17:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:18:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:19:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:20:00.022Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T07:20:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"4a47da10-4985-4eed-a60c-79ee4d119ba6","timestamp":"2025-07-31T07:20:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T07:20:00.029Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T07:20:00.081Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T07:20:00.083Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:21:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:22:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:23:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:24:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:25:00.019Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T07:25:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"97f6972e-503e-4291-ae2c-d71bbdc51295","timestamp":"2025-07-31T07:25:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T07:25:00.029Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T07:25:00.055Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T07:25:00.055Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:26:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:27:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:28:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:29:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:30:00.016Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T07:30:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8c26ca2a-6f86-42ab-9499-8dbb540fd04d","timestamp":"2025-07-31T07:30:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T07:30:00.026Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T07:30:00.062Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T07:30:00.064Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:31:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:32:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:33:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:34:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:35:00.017Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T07:35:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"18c6f5e9-7128-426a-b771-31e21e7b5cb1","timestamp":"2025-07-31T07:35:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T07:35:00.041Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T07:35:00.059Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T07:35:00.059Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:36:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:37:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:38:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:39:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:40:00.016Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T07:40:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"e1584bb3-265d-4003-9632-49d7e7c819ea","timestamp":"2025-07-31T07:40:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T07:40:00.040Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T07:40:00.101Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T07:40:00.104Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:41:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:42:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:43:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:44:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:45:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T07:45:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ee3e4f7a-9f77-4697-ac47-98492ad6afc6","timestamp":"2025-07-31T07:45:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T07:45:00.035Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T07:45:00.095Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T07:45:00.100Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:46:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:47:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:48:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:49:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:50:00.019Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T07:50:00.028Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"a7f8b779-60ab-48a5-9030-687b29f1c74f","timestamp":"2025-07-31T07:50:00.028Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T07:50:00.056Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T07:50:00.125Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T07:50:00.126Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:51:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:52:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:53:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:54:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:55:00.017Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T07:55:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"7bdf7555-2d81-4eba-a0e5-062834de364a","timestamp":"2025-07-31T07:55:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T07:55:00.028Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T07:55:00.090Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T07:55:00.094Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:56:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:57:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:58:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T07:59:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:00:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T08:00:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"3495f06d-0b4d-49a7-bd7f-99c0ea27650a","timestamp":"2025-07-31T08:00:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T08:00:00.040Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T08:00:00.083Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T08:00:00.085Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:01:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:02:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:03:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:04:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:05:00.019Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T08:05:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"478708e0-2732-4932-84c5-988a4359806f","timestamp":"2025-07-31T08:05:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T08:05:00.054Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T08:05:00.109Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T08:05:00.110Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:06:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:07:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:08:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:09:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:10:00.022Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T08:10:00.031Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d956895b-26b5-48f8-a01f-a243b8fb7004","timestamp":"2025-07-31T08:10:00.031Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T08:10:00.050Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T08:10:00.160Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T08:10:00.166Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:11:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:12:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:13:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:14:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:15:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T08:15:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8f5d3107-2564-4a29-867a-c5b68e873f8f","timestamp":"2025-07-31T08:15:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T08:15:00.045Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T08:15:00.123Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T08:15:00.124Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:16:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:17:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:18:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:19:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:20:00.007Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T08:20:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"0cbb53ec-a47c-44e2-aa06-98ef2e4b30af","timestamp":"2025-07-31T08:20:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T08:20:00.021Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T08:20:00.080Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T08:20:00.080Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:21:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:22:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:23:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:24:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:25:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T08:25:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"89ee3254-168f-4be1-97cf-365d60ce74a8","timestamp":"2025-07-31T08:25:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T08:25:00.041Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T08:25:00.111Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T08:25:00.112Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:26:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:27:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:28:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:29:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:30:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T08:30:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"56dc22f4-446d-4a5f-a258-174783c3046e","timestamp":"2025-07-31T08:30:00.007Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T08:30:00.017Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T08:30:00.055Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T08:30:00.055Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:31:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:32:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:33:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:34:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:35:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T08:35:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"80847d74-dcfb-4e0a-8a92-f722b8a6f10d","timestamp":"2025-07-31T08:35:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T08:35:00.012Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T08:35:00.058Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T08:35:00.059Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:36:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:37:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:38:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:39:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:40:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T08:40:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"0a01448b-60f8-4b70-b05a-f9f634d80880","timestamp":"2025-07-31T08:40:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T08:40:00.019Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T08:40:00.115Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T08:40:00.117Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:41:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:42:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:43:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:44:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:45:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T08:45:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8edbf43e-b44b-4863-81bb-7f9e774b7e18","timestamp":"2025-07-31T08:45:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T08:45:00.025Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T08:45:00.061Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T08:45:00.062Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:46:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:47:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:48:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:49:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:50:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T08:50:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"84904286-9151-497a-8106-5bd3d5aaf124","timestamp":"2025-07-31T08:50:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T08:50:00.044Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T08:50:00.116Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T08:50:00.123Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:51:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:52:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:53:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:54:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:55:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T08:55:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"1aaabec6-76c8-41dd-bf55-9976a2a33263","timestamp":"2025-07-31T08:55:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T08:55:00.024Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T08:55:00.030Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T08:55:00.031Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:56:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:57:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:58:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T08:59:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:00:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T09:00:00.029Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"923312b3-d0b9-4222-9760-95031db1225c","timestamp":"2025-07-31T09:00:00.029Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T09:00:00.050Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T09:00:00.064Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T09:00:00.069Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:01:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:02:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:03:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:04:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:05:00.019Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T09:05:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8139caa1-e252-402c-809a-d44409925486","timestamp":"2025-07-31T09:05:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T09:05:00.041Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T09:05:00.049Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T09:05:00.050Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:06:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:07:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:08:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:09:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:10:00.004Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T09:10:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"6de41241-1bcb-4c52-b870-6bb656b70f94","timestamp":"2025-07-31T09:10:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T09:10:00.016Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T09:10:00.058Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T09:10:00.060Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:11:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:12:00.415Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:13:00.050Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:14:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:15:00.017Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T09:15:00.083Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"5cb20f4e-05d7-4ccd-836d-8221dfd832de","timestamp":"2025-07-31T09:15:00.090Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T09:15:00.523Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T09:15:00.713Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T09:15:00.715Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:16:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:17:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:18:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:19:00.084Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:20:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T09:20:00.041Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"39db7edf-bc05-4050-9c38-6690225e818b","timestamp":"2025-07-31T09:20:00.042Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T09:20:00.137Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T09:20:00.209Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T09:20:00.212Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:21:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:22:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:23:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:24:00.060Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:25:00.017Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T09:25:00.037Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"0fd67c13-293a-409a-91ae-a058960ad0a9","timestamp":"2025-07-31T09:25:00.040Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T09:25:00.173Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T09:25:00.303Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T09:25:00.305Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:26:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:27:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:28:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:29:00.041Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:30:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T09:30:00.027Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8bf6e7d3-a67e-4d0f-8149-43347abd7f4b","timestamp":"2025-07-31T09:30:00.028Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T09:30:00.050Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T09:30:00.068Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T09:30:00.068Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:31:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:32:00.148Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:33:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:34:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:35:00.006Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T09:35:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"0cb6ab75-e34b-4036-b899-f1c2658373b6","timestamp":"2025-07-31T09:35:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T09:35:00.042Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T09:35:00.056Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T09:35:00.058Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:36:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:37:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:38:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:39:00.110Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:40:00.046Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T09:40:00.061Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"b7d5eb53-15f8-48f6-9d08-3dc867526d3f","timestamp":"2025-07-31T09:40:00.082Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T09:40:00.793Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T09:40:00.913Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T09:40:00.915Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:41:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:42:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:43:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:44:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:45:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T09:45:00.049Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"acce57a5-0b9c-4439-8842-7470c2c1bac9","timestamp":"2025-07-31T09:45:00.049Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T09:45:00.166Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T09:45:00.210Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T09:45:00.211Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:46:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:47:00.028Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:48:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:49:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:50:00.021Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T09:50:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8bdc2937-83c0-4a1f-9aff-ed24f630569b","timestamp":"2025-07-31T09:50:00.028Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T09:50:00.085Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T09:50:00.145Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T09:50:00.151Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:51:00.056Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:52:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:53:00.038Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:54:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:55:00.037Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T09:55:00.072Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"43e51f5d-b928-45a4-b8a7-91118fc96a22","timestamp":"2025-07-31T09:55:00.080Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T09:55:00.564Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T09:55:00.685Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T09:55:00.688Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:56:00.103Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:57:00.082Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:58:00.098Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T09:59:00.129Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:00:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T10:00:00.038Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"69f1d139-5995-4ae4-bcda-39f3991b9f6b","timestamp":"2025-07-31T10:00:00.043Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T10:00:00.338Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T10:00:00.401Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T10:00:00.404Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:01:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:02:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:03:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:04:00.047Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:05:00.034Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T10:05:00.117Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"4a97a04f-7444-4202-b028-55672a030ee0","timestamp":"2025-07-31T10:05:00.135Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T10:05:00.504Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T10:05:00.686Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T10:05:00.689Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:06:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:07:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:08:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:09:00.066Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:10:00.029Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T10:10:00.032Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ed0b97fc-a434-4782-b20f-0f7fb5dc3ee9","timestamp":"2025-07-31T10:10:00.042Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T10:10:00.056Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T10:10:00.101Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T10:10:00.102Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:11:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:12:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:13:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:14:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:15:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T10:15:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d809516d-684a-475e-a123-56904e5336b2","timestamp":"2025-07-31T10:15:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T10:15:00.031Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T10:15:00.069Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T10:15:00.069Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:16:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:17:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:18:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:19:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:20:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T10:20:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"62c6eb39-15f9-4512-a2ff-17268018c34e","timestamp":"2025-07-31T10:20:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T10:20:00.042Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T10:20:00.116Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T10:20:00.118Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:21:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:22:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:23:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:24:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:25:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T10:25:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d0c334b7-eebf-4fb6-9afa-4ce024ac674a","timestamp":"2025-07-31T10:25:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T10:25:00.038Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T10:25:00.096Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T10:25:00.097Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:26:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:27:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:28:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:29:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:30:00.025Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T10:30:00.035Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"e6d94204-ec12-43ee-9253-35183940b8ca","timestamp":"2025-07-31T10:30:00.036Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T10:30:00.102Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T10:30:00.180Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T10:30:00.182Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:31:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:32:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:33:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:34:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:35:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T10:35:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"54bb8e76-4da4-40d4-a4ed-9c7e022c5e78","timestamp":"2025-07-31T10:35:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T10:35:00.028Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T10:35:00.098Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T10:35:00.100Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:36:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:37:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:38:00.000Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:39:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:40:00.006Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T10:40:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"4d924cbc-5e33-48b1-8bda-4f5d3d83e126","timestamp":"2025-07-31T10:40:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T10:40:00.030Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T10:40:00.087Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T10:40:00.092Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:41:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:42:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:43:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:44:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:45:00.006Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T10:45:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"21010767-a786-4140-8988-41121bb2ecdd","timestamp":"2025-07-31T10:45:00.014Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T10:45:00.021Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T10:45:00.057Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T10:45:00.058Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:46:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:47:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:48:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:49:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:50:00.016Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T10:50:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"bdb3ad7b-26b1-469d-8028-7a204ec9608f","timestamp":"2025-07-31T10:50:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T10:50:00.037Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T10:50:00.073Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T10:50:00.073Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:51:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:52:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:53:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:54:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:55:00.017Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T10:55:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"4f34fed2-b9a8-4f7b-80a2-16bff09f278f","timestamp":"2025-07-31T10:55:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T10:55:00.027Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T10:55:00.069Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T10:55:00.069Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:56:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:57:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:58:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T10:59:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:00:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T11:00:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"cbe47569-68ff-4aa1-b458-d0c50b95c8ab","timestamp":"2025-07-31T11:00:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T11:00:00.036Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T11:00:00.067Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T11:00:00.070Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:01:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:02:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:03:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:04:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:05:00.102Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T11:05:00.110Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d089ef7e-8d0c-41b4-b09f-a6e5c6b7b1b2","timestamp":"2025-07-31T11:05:00.111Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T11:05:00.131Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T11:05:00.229Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T11:05:00.232Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:06:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:07:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:08:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:09:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:10:00.013Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T11:10:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d257a2cd-8327-4ec0-962f-fad9caae0990","timestamp":"2025-07-31T11:10:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T11:10:00.031Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T11:10:00.099Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T11:10:00.103Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:11:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:12:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:13:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:14:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:15:00.004Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T11:15:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"79305756-8c81-40d4-bf96-7da2fa6893ef","timestamp":"2025-07-31T11:15:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T11:15:00.025Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T11:15:00.078Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T11:15:00.079Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:16:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:17:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:18:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:19:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:20:00.019Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T11:20:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8d6472eb-6ce2-42bd-a748-2c9d667d9931","timestamp":"2025-07-31T11:20:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T11:20:00.033Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T11:20:00.089Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T11:20:00.090Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:21:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:22:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:23:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:24:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:25:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T11:25:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"1be49ab5-07e5-433d-acfb-f1a01aca7b88","timestamp":"2025-07-31T11:25:00.031Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T11:25:00.044Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T11:25:00.092Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T11:25:00.100Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:26:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:27:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:28:00.051Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:29:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:30:00.014Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T11:30:00.043Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"2f2eb1f1-fe38-4f31-bc29-20b8d6dc4da7","timestamp":"2025-07-31T11:30:00.053Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T11:30:00.470Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T11:30:00.697Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T11:30:00.699Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:31:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:32:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:33:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:34:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:35:00.008Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T11:35:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"970e1734-6cf8-44ab-840b-330efc4deec3","timestamp":"2025-07-31T11:35:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T11:35:00.039Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T11:35:00.116Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T11:35:00.122Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:36:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:37:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:38:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:39:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:40:00.008Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T11:40:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"43d394f6-2220-4dc7-86bb-df5c27428bd4","timestamp":"2025-07-31T11:40:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T11:40:00.028Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T11:40:00.065Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T11:40:00.066Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:41:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:42:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:43:00.027Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:44:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:45:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T11:45:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"1936cdef-a441-4fdc-9418-729c6b47b993","timestamp":"2025-07-31T11:45:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T11:45:00.052Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T11:45:00.159Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T11:45:00.161Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:46:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:47:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:48:00.037Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:49:00.034Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:50:00.019Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T11:50:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"f892c971-281c-4c18-85c0-6b3a51e7a15e","timestamp":"2025-07-31T11:50:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T11:50:00.109Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T11:50:00.223Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T11:50:00.225Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:51:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:52:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:53:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:54:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:55:00.016Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T11:55:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"f2e82094-eb66-4ac6-871d-7f510cdc2d6b","timestamp":"2025-07-31T11:55:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T11:55:00.033Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T11:55:00.090Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T11:55:00.092Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:56:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:57:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:58:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T11:59:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:00:00.011Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T12:00:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"90000b5b-ad7a-496b-b1c1-1a62bab2901d","timestamp":"2025-07-31T12:00:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T12:00:00.028Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T12:00:00.034Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T12:00:00.035Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:01:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:02:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:03:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:04:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:05:00.014Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T12:05:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"0e0f91f3-3eb8-4f36-9250-ea4848548aeb","timestamp":"2025-07-31T12:05:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T12:05:00.031Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T12:05:00.041Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T12:05:00.044Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:06:00.065Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:07:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:08:00.112Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:09:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:10:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T12:10:00.037Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"b6f20f68-12df-46d3-b449-514cb560d780","timestamp":"2025-07-31T12:10:00.042Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T12:10:00.387Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T12:10:00.442Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T12:10:00.444Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:11:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:12:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:13:00.085Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:14:00.068Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:15:00.024Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T12:15:00.058Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8a1c5389-05cb-4aed-935d-28c2cc20a1d4","timestamp":"2025-07-31T12:15:00.062Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T12:15:00.090Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T12:15:00.103Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T12:15:00.105Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:16:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:17:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:18:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:19:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:20:00.009Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T12:20:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"0b797944-9ec9-4cb0-ac27-22530e491951","timestamp":"2025-07-31T12:20:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T12:20:00.030Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T12:20:00.038Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T12:20:00.039Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:21:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:22:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:23:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:24:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:25:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T12:25:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"19f3883a-64aa-4da6-850d-d0ad45b46b58","timestamp":"2025-07-31T12:25:00.010Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T12:25:00.029Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T12:25:00.036Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T12:25:00.037Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:26:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:27:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:28:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:29:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:30:00.017Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T12:30:00.035Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"13e5d96a-e5f6-4cad-b1aa-acecc10904c3","timestamp":"2025-07-31T12:30:00.041Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T12:30:00.203Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T12:30:00.243Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T12:30:00.245Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:31:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:32:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:33:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:34:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:35:00.012Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T12:35:00.051Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"66fef15b-956a-4199-8d41-c09f3f2ea33f","timestamp":"2025-07-31T12:35:00.052Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T12:35:00.088Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T12:35:00.102Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T12:35:00.105Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:36:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:37:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:38:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:39:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:40:00.011Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T12:40:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"36f02b02-7957-4680-85f2-8b040815c27a","timestamp":"2025-07-31T12:40:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T12:40:00.052Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T12:40:00.066Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T12:40:00.066Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:41:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:42:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:43:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:44:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:45:00.014Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T12:45:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"3363381a-c452-4431-a24b-28320748012b","timestamp":"2025-07-31T12:45:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T12:45:00.025Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T12:45:00.071Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T12:45:00.074Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:46:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:47:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:48:00.030Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:49:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:50:00.023Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T12:50:00.048Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"7c10bd2b-0c74-4e7d-8729-cd158526713a","timestamp":"2025-07-31T12:50:00.053Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T12:50:00.248Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T12:50:00.360Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T12:50:00.361Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:51:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:52:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:53:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:54:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:55:00.015Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T12:55:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"567e9606-4699-4a14-8018-ea76113908f7","timestamp":"2025-07-31T12:55:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T12:55:00.030Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T12:55:00.080Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T12:55:00.081Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:56:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:57:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:58:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T12:59:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:00:00.011Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T13:00:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ee48a206-5e6a-4897-be0e-7f550e346ba3","timestamp":"2025-07-31T13:00:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T13:00:00.064Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T13:00:00.179Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T13:00:00.182Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:01:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:02:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:03:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:04:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:05:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T13:05:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"60f7996c-e560-4b8e-9669-1494cfe59f1a","timestamp":"2025-07-31T13:05:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T13:05:00.050Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T13:05:00.124Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T13:05:00.128Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:06:00.227Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:07:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:08:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:09:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:10:00.020Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T13:10:00.029Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"7f0e50d7-6e7a-4de0-828e-7f0b3c05a5ba","timestamp":"2025-07-31T13:10:00.038Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T13:10:00.329Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T13:10:00.467Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T13:10:00.469Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:11:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:12:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:13:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:14:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:15:00.008Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T13:15:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d3da699c-88f8-4933-ad63-377e2e0cea1a","timestamp":"2025-07-31T13:15:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T13:15:00.027Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T13:15:00.087Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T13:15:00.087Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:16:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:17:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:18:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:19:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:20:00.012Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T13:20:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"778ca298-2280-4a0b-94ac-adf5254f798f","timestamp":"2025-07-31T13:20:00.024Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T13:20:00.059Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T13:20:00.072Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T13:20:00.073Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:21:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:22:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:23:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:24:00.052Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:25:00.012Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T13:25:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"b6c5e7cf-2f38-4298-bcc9-1849c16c735a","timestamp":"2025-07-31T13:25:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T13:25:00.054Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T13:25:00.111Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T13:25:00.120Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:26:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:27:00.060Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:28:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:29:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:30:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T13:30:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"757594a9-a7fe-4d8f-8e4a-c05562085b44","timestamp":"2025-07-31T13:30:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T13:30:00.339Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T13:30:00.537Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T13:30:00.542Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:31:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:32:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:33:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:34:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:35:00.001Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T13:35:00.005Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"fde23df1-7c9a-42be-8872-7a5c8a6109d9","timestamp":"2025-07-31T13:35:00.006Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T13:35:00.012Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T13:35:00.017Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T13:35:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:36:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:37:00.047Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:38:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:39:00.023Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:40:00.002Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T13:40:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"4f74a379-0646-4687-9725-3ba21dcb79d7","timestamp":"2025-07-31T13:40:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T13:40:00.202Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T13:40:00.260Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T13:40:00.262Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:41:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:42:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:43:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:44:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:45:00.012Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T13:45:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"a69c9f27-491e-4dc5-b939-2e1cab987aec","timestamp":"2025-07-31T13:45:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T13:45:00.106Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T13:45:00.150Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T13:45:00.157Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:46:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:47:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:48:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:49:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:50:00.006Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T13:50:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"90f6720c-780e-4d0e-8a54-34deb36fb3d2","timestamp":"2025-07-31T13:50:00.026Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T13:50:00.032Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T13:50:00.038Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T13:50:00.038Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:51:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:52:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:53:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:54:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:55:00.019Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T13:55:00.036Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"bb0b7497-3cda-400a-bf2a-1860237245be","timestamp":"2025-07-31T13:55:00.037Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T13:55:00.076Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T13:55:00.098Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T13:55:00.101Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:56:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:57:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:58:00.030Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T13:59:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:00:00.004Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T14:00:00.017Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d7428050-0788-4ba9-81c2-94e762a4a081","timestamp":"2025-07-31T14:00:00.021Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T14:00:00.152Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T14:00:00.185Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T14:00:00.186Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:01:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:02:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:03:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:04:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:05:00.019Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T14:05:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"ef044353-ed06-417a-a5a2-db167d6a2548","timestamp":"2025-07-31T14:05:00.023Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T14:05:00.051Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T14:05:00.065Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T14:05:00.066Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:06:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:07:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:08:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:09:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:10:00.007Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T14:10:00.009Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"4828315d-0933-4a9d-b4d4-04264dac1037","timestamp":"2025-07-31T14:10:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T14:10:00.031Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T14:10:00.039Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T14:10:00.040Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:11:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:12:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:13:00.022Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:14:00.075Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:15:00.009Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T14:15:00.048Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"07001cb8-2283-4383-a7bb-1e235634fc41","timestamp":"2025-07-31T14:15:00.050Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T14:15:00.159Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T14:15:00.242Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T14:15:00.243Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:16:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:17:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:18:00.029Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:19:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:20:00.004Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T14:20:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"483ffc51-c688-4aee-b67b-01fc631516df","timestamp":"2025-07-31T14:20:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T14:20:00.025Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T14:20:00.031Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T14:20:00.031Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:21:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:22:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:23:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:24:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:25:00.016Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T14:25:00.018Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"4707f10e-94dd-4ebe-bf4c-c57d897764e3","timestamp":"2025-07-31T14:25:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T14:25:00.032Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T14:25:00.042Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T14:25:00.043Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:26:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:27:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:28:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:29:00.009Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:30:00.008Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T14:30:00.013Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"2f888b98-074e-4313-8b76-02006a4a13b7","timestamp":"2025-07-31T14:30:00.029Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T14:30:00.090Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T14:30:00.132Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T14:30:00.134Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:31:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:32:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:33:00.020Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:34:00.019Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:35:00.110Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T14:35:00.165Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d6fb2f90-32ca-4a34-824f-a5372a0642c1","timestamp":"2025-07-31T14:35:00.166Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T14:35:00.213Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T14:35:00.236Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T14:35:00.236Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:36:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:37:00.002Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:38:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:39:00.039Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:40:00.010Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T14:40:00.019Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"7790e716-34ad-4717-90d4-8aac5135ef82","timestamp":"2025-07-31T14:40:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T14:40:00.035Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T14:40:00.044Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T14:40:00.045Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:41:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:42:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:43:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:44:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:45:00.016Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T14:45:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"e37978da-bf59-4f03-8cf6-28a530ed7dee","timestamp":"2025-07-31T14:45:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T14:45:00.042Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T14:45:00.055Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T14:45:00.057Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:46:00.013Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:47:00.012Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:48:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:49:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:50:00.008Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T14:50:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"86104924-f87e-488a-b350-2dda12d5d7c9","timestamp":"2025-07-31T14:50:00.012Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T14:50:00.032Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T14:50:00.047Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T14:50:00.047Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:51:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:52:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:53:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:54:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:55:00.005Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T14:55:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"f3a9492d-5b35-4ba0-b91b-2586786bfcea","timestamp":"2025-07-31T14:55:00.011Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T14:55:00.016Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T14:55:00.021Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T14:55:00.021Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:56:00.008Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:57:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:58:00.032Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T14:59:00.018Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:00:00.018Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T15:00:00.036Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"82aebaf5-d627-4a43-9899-9125f5cc7772","timestamp":"2025-07-31T15:00:00.036Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T15:00:00.067Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T15:00:00.107Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T15:00:00.108Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:01:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:02:00.045Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:03:00.034Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:04:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:05:00.007Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T15:05:00.025Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"4893b707-7f6f-4794-b9d4-5f30b10d255f","timestamp":"2025-07-31T15:05:00.030Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T15:05:00.263Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T15:05:00.390Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T15:05:00.395Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:06:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:07:00.037Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:08:00.005Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:09:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:10:00.622Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T15:10:01.145Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"f2c64b79-a210-4260-b6fe-42244b871b49","timestamp":"2025-07-31T15:10:01.152Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T15:10:01.998Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T15:10:02.555Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T15:10:02.561Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:11:00.066Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:12:00.016Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:13:00.055Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:14:00.090Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:15:00.074Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T15:15:00.147Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"71af398c-2d7b-4e1b-b46c-4536d69e374a","timestamp":"2025-07-31T15:15:00.164Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T15:15:00.542Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T15:15:00.642Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T15:15:00.643Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:16:00.024Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:17:00.101Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:18:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:19:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:20:00.035Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T15:20:00.038Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"29b28775-1d97-4ff2-99c4-0b634238e806","timestamp":"2025-07-31T15:20:00.039Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T15:20:00.066Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T15:20:00.076Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T15:20:00.078Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:21:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:22:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:23:00.010Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:24:00.190Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:25:00.038Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T15:25:00.074Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"cd582d3d-7180-4957-a73a-4367fd38de9b","timestamp":"2025-07-31T15:25:00.079Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T15:25:00.298Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T15:25:00.462Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T15:25:00.466Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:26:00.001Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:27:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:28:00.045Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:29:00.044Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:30:00.016Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T15:30:00.055Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"4bf20395-0b97-4f1f-9229-8e69d066a53e","timestamp":"2025-07-31T15:30:00.055Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T15:30:00.091Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T15:30:00.125Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T15:30:00.128Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:31:00.003Z"}
{"label":"Notifications","level":"info","message":"Sending notification(s) for MEDIA_AUTO_APPROVED","subject":"Chernobyl (2019)","timestamp":"2025-07-31T15:31:48.595Z"}
{"label":"Media Request","level":"info","mediaId":73,"message":"Requester has no active tag. Creating new","newTag":"1 - Tankeeee2_GAMES","requestId":13,"timestamp":"2025-07-31T15:31:48.998Z","userId":1}
{"label":"Media Request","level":"info","mediaId":73,"message":"Sent request to Sonarr","requestId":13,"timestamp":"2025-07-31T15:31:49.227Z"}
{"label":"Sonarr","level":"info","message":"Sonarr accepted request","timestamp":"2025-07-31T15:31:50.349Z"}
{"label":"Sonarr","level":"debug","message":"Sonarr add details","series":{"addOptions":{"ignoreEpisodesWithFiles":true,"ignoreEpisodesWithoutFiles":false,"monitor":"unknown","searchForCutoffUnmetEpisodes":false,"searchForMissingEpisodes":true},"added":"2025-07-31T15:31:50Z","airTime":"21:00","alternateTitles":[],"certification":"TV-MA","cleanTitle":"chernobyl","ended":true,"firstAired":"2019-05-06T00:00:00Z","genres":["Drama","History","Mini-Series","Thriller"],"id":68,"images":[{"coverType":"banner","remoteUrl":"https://artworks.thetvdb.com/banners/v4/series/360893/banners/6751caac5a993.jpg","url":"/MediaCover/68/banner.jpg"},{"coverType":"poster","remoteUrl":"https://artworks.thetvdb.com/banners/posters/5cc12861c93e4.jpg","url":"/MediaCover/68/poster.jpg"},{"coverType":"fanart","remoteUrl":"https://artworks.thetvdb.com/banners/series/360893/backgrounds/62017319.jpg","url":"/MediaCover/68/fanart.jpg"},{"coverType":"clearlogo","remoteUrl":"https://artworks.thetvdb.com/banners/v4/series/360893/clearlogo/611be4d52d6a8.png","url":"/MediaCover/68/clearlogo.png"}],"imdbId":"tt7366338","languageProfileId":1,"lastAired":"2019-06-03T00:00:00Z","monitorNewItems":"all","monitored":true,"network":"HBO","originalLanguage":{"id":1,"name":"English"},"overview":"Chernobyl dramatizes the story of the 1986 nuclear accident — one of the worst man-made catastrophes in history — and the sacrifices made to save Europe from unimaginable disaster.","path":"/CONTENIDO/SERIES/Chernobyl","qualityProfileId":7,"ratings":{"value":9.3,"votes":964260},"rootFolderPath":"/CONTENIDO/SERIES","runtime":65,"seasonFolder":true,"seasons":[{"monitored":true,"seasonNumber":1}],"seriesType":"standard","sortTitle":"chernobyl","statistics":{"episodeCount":0,"episodeFileCount":0,"percentOfEpisodes":0,"seasonCount":1,"sizeOnDisk":0,"totalEpisodeCount":0},"status":"ended","tags":[1],"title":"Chernobyl","titleSlug":"chernobyl","tmdbId":87108,"tvMazeId":30770,"tvRageId":0,"tvdbId":360893,"useSceneNumbering":false,"year":2019},"timestamp":"2025-07-31T15:31:50.350Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:32:00.017Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:33:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:34:00.027Z"}
{"label":"Download Tracker","level":"debug","message":"Found 1 item(s) in progress on Sonarr server: Sonarr","timestamp":"2025-07-31T15:34:00.140Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:35:00.048Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T15:35:00.085Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"8591c0e6-2819-4197-89de-71b0e35cbb0e","timestamp":"2025-07-31T15:35:00.099Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T15:35:00.329Z"}
{"label":"Download Tracker","level":"debug","message":"Found 1 item(s) in progress on Sonarr server: Sonarr","timestamp":"2025-07-31T15:35:00.405Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T15:35:00.418Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T15:35:00.419Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:36:00.006Z"}
{"label":"Download Tracker","level":"debug","message":"Found 1 item(s) in progress on Sonarr server: Sonarr","timestamp":"2025-07-31T15:36:00.068Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:37:00.011Z"}
{"label":"Download Tracker","level":"debug","message":"Found 1 item(s) in progress on Sonarr server: Sonarr","timestamp":"2025-07-31T15:37:00.217Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:38:00.000Z"}
{"label":"Download Tracker","level":"debug","message":"Found 1 item(s) in progress on Sonarr server: Sonarr","timestamp":"2025-07-31T15:38:00.107Z"}
{"label":"Media","level":"error","message":"[Radarr] Failed to remove serie: Request failed with status code 404","timestamp":"2025-07-31T15:38:13.430Z"}
{"label":"Notifications","level":"info","message":"Sending notification(s) for MEDIA_AUTO_APPROVED","subject":"Chernobyl (2019)","timestamp":"2025-07-31T15:38:17.012Z"}
{"label":"Media Request","level":"info","mediaId":74,"message":"Sent request to Sonarr","requestId":14,"timestamp":"2025-07-31T15:38:17.092Z"}
{"label":"Sonarr","level":"info","message":"Sonarr accepted request","timestamp":"2025-07-31T15:38:17.404Z"}
{"label":"Sonarr","level":"debug","message":"Sonarr add details","series":{"addOptions":{"ignoreEpisodesWithFiles":true,"ignoreEpisodesWithoutFiles":false,"monitor":"unknown","searchForCutoffUnmetEpisodes":false,"searchForMissingEpisodes":true},"added":"2025-07-31T15:38:17Z","airTime":"21:00","alternateTitles":[],"certification":"TV-MA","cleanTitle":"chernobyl","ended":true,"firstAired":"2019-05-06T00:00:00Z","genres":["Drama","History","Mini-Series","Thriller"],"id":69,"images":[{"coverType":"banner","remoteUrl":"https://artworks.thetvdb.com/banners/v4/series/360893/banners/6751caac5a993.jpg","url":"/MediaCover/69/banner.jpg"},{"coverType":"poster","remoteUrl":"https://artworks.thetvdb.com/banners/posters/5cc12861c93e4.jpg","url":"/MediaCover/69/poster.jpg"},{"coverType":"fanart","remoteUrl":"https://artworks.thetvdb.com/banners/series/360893/backgrounds/62017319.jpg","url":"/MediaCover/69/fanart.jpg"},{"coverType":"clearlogo","remoteUrl":"https://artworks.thetvdb.com/banners/v4/series/360893/clearlogo/611be4d52d6a8.png","url":"/MediaCover/69/clearlogo.png"}],"imdbId":"tt7366338","languageProfileId":1,"lastAired":"2019-06-03T00:00:00Z","monitorNewItems":"all","monitored":true,"network":"HBO","originalLanguage":{"id":1,"name":"English"},"overview":"Chernobyl dramatizes the story of the 1986 nuclear accident — one of the worst man-made catastrophes in history — and the sacrifices made to save Europe from unimaginable disaster.","path":"/CONTENIDO/SERIES/Chernobyl","qualityProfileId":7,"ratings":{"value":9.3,"votes":964260},"rootFolderPath":"/CONTENIDO/SERIES","runtime":65,"seasonFolder":true,"seasons":[{"monitored":true,"seasonNumber":1}],"seriesType":"standard","sortTitle":"chernobyl","statistics":{"episodeCount":0,"episodeFileCount":0,"percentOfEpisodes":0,"seasonCount":1,"sizeOnDisk":0,"totalEpisodeCount":0},"status":"ended","tags":[1],"title":"Chernobyl","titleSlug":"chernobyl","tmdbId":87108,"tvMazeId":30770,"tvRageId":0,"tvdbId":360893,"useSceneNumbering":false,"year":2019},"timestamp":"2025-07-31T15:38:17.411Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:39:00.014Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:40:00.032Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T15:40:00.095Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"690a4101-86f9-4633-94c0-24f56bf08e2c","timestamp":"2025-07-31T15:40:00.103Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T15:40:00.710Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T15:40:01.114Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T15:40:01.121Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:41:00.026Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:42:00.036Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:43:00.039Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:44:00.300Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:45:00.058Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T15:45:00.112Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"b5dc9c37-c062-424c-9c25-bd06459b23b9","timestamp":"2025-07-31T15:45:00.113Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T15:45:00.284Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T15:45:00.338Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T15:45:00.339Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:46:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:47:00.093Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:48:00.002Z"}
{"level":"info","message":"Commit Tag: $GIT_SHA","timestamp":"2025-07-31T15:50:42.103Z"}
{"level":"info","message":"Starting Jellyseerr version 2.7.2","timestamp":"2025-07-31T15:50:44.943Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0001_migrate_hostname.js'...","timestamp":"2025-07-31T15:50:51.676Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0002_migrate_apitokens.js'...","timestamp":"2025-07-31T15:50:51.690Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0003_emby_media_server_type.js'...","timestamp":"2025-07-31T15:50:51.729Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0004_migrate_region_setting.js'...","timestamp":"2025-07-31T15:50:51.743Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0005_migrate_network_settings.js'...","timestamp":"2025-07-31T15:50:51.753Z"}
{"label":"Settings Migrator","level":"debug","message":"Checking migration '0006_remove_lunasea.js'...","timestamp":"2025-07-31T15:50:51.796Z"}
{"label":"Notifications","level":"info","message":"Registered notification agents","timestamp":"2025-07-31T15:50:51.875Z"}
{"label":"Jobs","level":"info","message":"Scheduled jobs loaded","timestamp":"2025-07-31T15:50:52.299Z"}
{"label":"Server","level":"info","message":"Server ready on port 5055","timestamp":"2025-07-31T15:50:53.301Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:51:00.025Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:52:00.029Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:53:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:54:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:55:00.006Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T15:55:00.020Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"cbce1af9-59f5-40ce-a5e0-5bf032c9f31c","timestamp":"2025-07-31T15:55:00.022Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T15:55:00.087Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T15:55:00.217Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T15:55:00.219Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:56:00.036Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:57:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:58:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T15:59:00.006Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T16:00:00.003Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T16:00:00.015Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"d9ca5863-4bca-461b-8df9-db202f20093b","timestamp":"2025-07-31T16:00:00.016Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T16:00:00.052Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T16:00:00.112Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T16:00:00.113Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T16:01:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T16:02:00.003Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T16:03:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T16:04:00.004Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T16:05:00.004Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T16:05:00.028Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"a4592c35-ce96-41cf-ad8c-905dad113859","timestamp":"2025-07-31T16:05:00.028Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T16:05:00.156Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T16:05:00.241Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T16:05:00.242Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T16:06:00.296Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T16:07:00.037Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T16:08:00.038Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T16:09:00.015Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T16:10:00.047Z"}
{"label":"Jobs","level":"info","message":"Starting scheduled job: Jellyfin Recently Added Scan","timestamp":"2025-07-31T16:10:00.136Z"}
{"label":"Jellyfin Sync","level":"info","message":"Jellyfin Sync Starting","sessionId":"e88e2475-db72-4079-8492-469d6c16a2b1","timestamp":"2025-07-31T16:10:00.148Z"}
{"label":"Jellyfin Sync","level":"info","message":"Beginning to process recently added for library: Películas","timestamp":"2025-07-31T16:10:00.377Z"}
{"error":401,"label":"Jellyfin API","level":"error","message":"Something went wrong while getting library content from the Jellyfin server: Request failed with status code 401","timestamp":"2025-07-31T16:10:00.640Z"}
{"errorMessage":"","label":"Jellyfin Sync","level":"error","message":"Sync interrupted","timestamp":"2025-07-31T16:10:00.648Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T16:11:00.047Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T16:12:00.007Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T16:13:00.011Z"}
{"label":"Jobs","level":"debug","message":"Starting scheduled job: Download Sync","timestamp":"2025-07-31T16:14:00.016Z"}
