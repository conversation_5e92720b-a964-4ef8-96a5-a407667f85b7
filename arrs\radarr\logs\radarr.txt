2025-07-31 17:40:35.8|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 17:40:35.9|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 14:38:35 and 07/29/2025 15:40:35 UTC. Search may be required.
2025-07-31 17:40:36.1|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 17:40:36.3|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/31/2025 14:38:36 and 07/31/2025 15:40:36 UTC. Search may be required.
2025-07-31 17:40:36.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 14:38:36 and 07/31/2025 15:40:36 UTC. Search may be required.
2025-07-31 17:40:40.6|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 14:38:40 and 07/31/2025 15:40:40 UTC. Search may be required.
2025-07-31 17:40:44.2|Info|DownloadDecisionMaker|Processing 615 releases
2025-07-31 17:41:39.5|Info|RssSyncService|RSS Sync Completed. Reports found: 615, Reports grabbed: 0
2025-07-31 17:50:19.1|Info|Bootstrap|Starting Radarr - /app/radarr/bin/Radarr - Version 5.26.2.10099
2025-07-31 17:50:20.4|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-07-31 17:50:20.7|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-07-31 17:50:24.4|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-07-31 17:50:25.8|Info|MigrationController|*** Migrating data source=/config/radarr.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-07-31 17:50:26.2|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-07-31 17:50:26.4|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-07-31 17:50:26.5|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-07-31 17:50:27.0|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-07-31 17:50:27.0|Info|FluentMigrator.Runner.MigrationRunner|=> 0.5459131s
2025-07-31 17:50:27.0|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-07-31 17:50:27.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.5742707s
2025-07-31 17:50:27.5|Info|MigrationController|*** Migrating data source=/config/logs.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-07-31 17:50:27.9|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-07-31 17:50:27.9|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-07-31 17:50:28.0|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-07-31 17:50:28.0|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-07-31 17:50:28.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.1236396s
2025-07-31 17:50:28.1|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-07-31 17:50:28.1|Info|FluentMigrator.Runner.MigrationRunner|=> 0.1515757s
2025-07-31 17:50:34.0|Info|Microsoft.Hosting.Lifetime|Now listening on: http://[::]:7878
2025-07-31 17:50:38.3|Info|CommandExecutor|Starting 2 threads for tasks.
2025-07-31 17:50:39.6|Info|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.
2025-07-31 17:50:39.6|Info|Microsoft.Hosting.Lifetime|Hosting environment: Production
2025-07-31 17:50:39.7|Info|Microsoft.Hosting.Lifetime|Content root path: /app/radarr/bin
2025-07-31 17:50:40.4|Info|ManagedHttpDispatcher|IPv4 is available: True, IPv6 will be disabled
2025-07-31 18:12:09.0|Info|RssSyncService|Starting RSS Sync
2025-07-31 18:12:11.7|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 15:40:35 and 07/29/2025 16:12:11 UTC. Search may be required.
2025-07-31 18:12:12.7|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 15:40:36 and 07/31/2025 16:12:11 UTC. Search may be required.
2025-07-31 18:12:12.7|Warn|Torznab|Indexer MoviesDVDR rss sync didn't cover the period between 07/31/2025 15:40:36 and 07/31/2025 16:12:11 UTC. Search may be required.
2025-07-31 18:12:13.1|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 18:12:13.2|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=movie&cat=2000,8000,100467,104627,112972,121527,127246,136409,146065,151062,5000,5070&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 18:12:15.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 15:40:40 and 07/31/2025 16:12:15 UTC. Search may be required.
2025-07-31 18:12:17.6|Info|DownloadDecisionMaker|Processing 615 releases
2025-07-31 18:12:56.0|Info|RssSyncService|RSS Sync Completed. Reports found: 615, Reports grabbed: 0
