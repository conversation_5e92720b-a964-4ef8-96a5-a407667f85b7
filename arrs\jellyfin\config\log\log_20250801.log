[2025-08-01 00:00:00.025 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Playback Reporting Trim Db" set to fire at 2025-08-02 00:00:00.000 +02:00, which is 23:59:59.9743904 from now.
[2025-08-01 00:00:00.098 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:00:00.098 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:00:00.098 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 00:00:19.528 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:00:39.586 +02:00] [INF] [10] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:00:56.026 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:00:56.123 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:00:59.654 +02:00] [INF] [8] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:01:19.681 +02:00] [INF] [10] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:01:39.739 +02:00] [INF] [8] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:01:59.755 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:02:19.775 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:02:27.027 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:02:27.124 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:02:39.803 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:02:59.847 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:03:19.869 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:03:39.904 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:03:58.025 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:03:58.121 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:03:59.922 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:04:19.948 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:04:40.000 +02:00] [INF] [8] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:05:00.033 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:05:00.033 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:05:00.033 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 00:05:00.037 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:05:20.040 +02:00] [INF] [12] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:05:29.025 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:05:29.121 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:05:40.098 +02:00] [INF] [12] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:06:00.124 +02:00] [INF] [8] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:06:20.152 +02:00] [INF] [21] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:06:40.177 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:07:00.026 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:07:00.120 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:07:00.206 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:07:20.232 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:07:40.264 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:08:00.322 +02:00] [INF] [8] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:08:20.335 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:08:31.022 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:08:31.120 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:08:40.414 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:09:00.445 +02:00] [INF] [10] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:09:20.491 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:09:40.542 +02:00] [INF] [10] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:10:00.028 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:10:00.028 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:10:00.029 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 00:10:00.548 +02:00] [INF] [8] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:10:02.018 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:10:02.114 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:10:20.575 +02:00] [INF] [8] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:10:40.615 +02:00] [INF] [8] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:11:00.658 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:11:20.677 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:11:33.013 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:11:33.106 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:11:40.707 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:12:00.742 +02:00] [INF] [8] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:12:20.770 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:12:40.822 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:13:00.859 +02:00] [INF] [8] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:13:04.012 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:13:04.108 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:13:21.864 +02:00] [INF] [12] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:13:41.922 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:14:02.915 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:14:22.962 +02:00] [INF] [12] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:14:35.007 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:14:35.104 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:14:43.034 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:15:00.043 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:15:00.043 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:15:00.043 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 00:15:04.026 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:15:24.075 +02:00] [INF] [12] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:15:44.121 +02:00] [INF] [12] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:16:04.175 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:16:06.006 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:16:06.103 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:16:24.192 +02:00] [INF] [12] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:16:44.235 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:17:04.296 +02:00] [INF] [10] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:17:24.326 +02:00] [INF] [10] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:17:37.004 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:17:37.100 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:17:44.420 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:18:04.424 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:18:24.465 +02:00] [INF] [10] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:18:44.530 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:19:04.576 +02:00] [INF] [12] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:19:08.002 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:19:08.098 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:19:24.605 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:19:44.640 +02:00] [INF] [12] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:20:00.028 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:20:00.028 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:20:00.029 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 00:20:04.677 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:20:24.692 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:20:38.998 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:20:39.095 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:20:44.748 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:21:04.806 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:21:24.830 +02:00] [INF] [8] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:21:44.890 +02:00] [INF] [10] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:22:05.144 +02:00] [INF] [10] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:22:09.998 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:22:10.094 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:22:25.176 +02:00] [INF] [10] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:22:45.223 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:23:05.248 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:23:25.271 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:23:40.995 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:23:41.091 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:23:45.320 +02:00] [INF] [21] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:24:05.337 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:24:25.373 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:24:45.416 +02:00] [INF] [10] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:25:00.027 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:25:00.027 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:25:00.027 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 00:25:05.440 +02:00] [INF] [10] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:25:11.996 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:25:12.089 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:25:25.481 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:25:45.533 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:26:05.553 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:26:25.579 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:26:42.995 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:26:43.087 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:26:45.604 +02:00] [INF] [12] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:27:05.638 +02:00] [INF] [12] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:27:25.668 +02:00] [INF] [10] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:27:45.714 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:28:05.763 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:28:13.991 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:28:14.083 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:28:25.800 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:28:45.809 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:29:05.874 +02:00] [INF] [10] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:29:25.885 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:29:44.992 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:29:45.084 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:29:45.927 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:30:00.030 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:30:00.030 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:30:00.030 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 00:30:06.016 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:30:26.042 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:30:46.109 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:31:06.161 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:31:15.988 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:31:16.081 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:31:26.196 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:31:46.278 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:32:06.314 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:32:26.357 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:32:46.427 +02:00] [INF] [10] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:32:46.990 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:32:47.082 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:33:06.501 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:33:26.518 +02:00] [INF] [10] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:33:46.592 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:34:06.624 +02:00] [INF] [12] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:34:17.985 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:34:18.077 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:34:26.671 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:34:46.694 +02:00] [INF] [10] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:35:00.035 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:35:00.035 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:35:00.035 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 00:35:06.753 +02:00] [INF] [12] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:35:26.762 +02:00] [INF] [10] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:35:46.796 +02:00] [INF] [12] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:35:48.986 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:35:49.079 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:36:06.823 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:36:26.850 +02:00] [INF] [20] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:36:46.888 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:37:06.929 +02:00] [INF] [8] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:37:19.983 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:37:20.075 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:37:26.946 +02:00] [INF] [12] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:37:46.984 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:38:07.006 +02:00] [INF] [12] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:38:27.043 +02:00] [INF] [12] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:38:47.083 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:38:50.979 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:38:51.070 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:39:07.123 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:39:27.123 +02:00] [INF] [12] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:39:47.149 +02:00] [INF] [12] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:40:00.028 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:40:00.029 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:40:00.029 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 00:40:07.209 +02:00] [INF] [3] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:40:21.981 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:40:22.073 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:40:27.211 +02:00] [INF] [8] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:40:47.247 +02:00] [INF] [12] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:41:07.303 +02:00] [INF] [19] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:41:27.327 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:41:47.371 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:41:52.976 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:41:53.067 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:42:07.437 +02:00] [INF] [17] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:42:27.482 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Processing playback tracker : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:42:41.761 +02:00] [INF] [12] Emby.Server.Implementations.Session.SessionManager: Playback stopped reported by app "Android TV" "0.18.11" playing "El grande". Stopped at "3294775" ms
[2025-08-01 00:42:41.787 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Playback stop tracker found, processing stop : "d0bffd0f7a32fafec4992d940745ade6cd82ddc1-a8d66ec8890940eaaa34a5a7c5e10f69-f6f387c55a8507e97918e803a0f40a15"
[2025-08-01 00:42:41.787 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.Data.PlaybackTracker: PlaybackTracker : Adding Stop Event : 08/01/2025 00:42:41
[2025-08-01 00:42:41.787 +02:00] [INF] [18] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: Saving playback tracking activity in DB
[2025-08-01 00:43:23.973 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:43:24.064 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:44:54.976 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:44:55.067 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:45:00.030 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:45:00.030 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:45:00.030 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 00:46:25.975 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:46:26.067 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:47:56.972 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:47:57.064 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:49:27.972 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:49:28.064 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:50:00.071 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:50:00.071 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:50:00.071 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 00:50:58.972 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:50:59.065 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:52:29.975 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:52:30.063 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:54:00.975 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:54:01.063 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:55:00.073 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:55:00.074 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 00:55:00.075 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 00:55:31.970 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:55:32.109 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:57:02.967 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:57:03.102 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:58:54.020 +02:00] [INF] [1] Main: Jellyfin version: "10.10.7"
[2025-08-01 00:58:54.222 +02:00] [INF] [1] Main: Environment Variables: ["[JELLYFIN_WEB_DIR, /jellyfin/jellyfin-web]", "[JELLYFIN_DATA_DIR, /config]", "[JELLYFIN_CACHE_DIR, /cache]", "[JELLYFIN_LOG_DIR, /config/log]", "[JELLYFIN_CONFIG_DIR, /config/config]", "[JELLYFIN_FFMPEG, /usr/lib/jellyfin-ffmpeg/ffmpeg]"]
[2025-08-01 00:58:54.244 +02:00] [INF] [1] Main: Arguments: ["/jellyfin/jellyfin.dll"]
[2025-08-01 00:58:54.260 +02:00] [INF] [1] Main: Operating system: "Debian GNU/Linux 12 (bookworm)"
[2025-08-01 00:58:54.263 +02:00] [INF] [1] Main: Architecture: X64
[2025-08-01 00:58:54.278 +02:00] [INF] [1] Main: 64-Bit Process: True
[2025-08-01 00:58:54.278 +02:00] [INF] [1] Main: User Interactive: True
[2025-08-01 00:58:54.278 +02:00] [INF] [1] Main: Processor count: 2
[2025-08-01 00:58:54.278 +02:00] [INF] [1] Main: Program data path: "/config"
[2025-08-01 00:58:54.278 +02:00] [INF] [1] Main: Log directory path: "/config/log"
[2025-08-01 00:58:54.278 +02:00] [INF] [1] Main: Config directory path: "/config/config"
[2025-08-01 00:58:54.278 +02:00] [INF] [1] Main: Cache path: "/cache"
[2025-08-01 00:58:54.301 +02:00] [INF] [1] Main: Temp directory path: "/tmp/jellyfin"
[2025-08-01 00:58:54.308 +02:00] [INF] [1] Main: Web resources path: "/jellyfin/jellyfin-web"
[2025-08-01 00:58:54.312 +02:00] [INF] [1] Main: Application directory: "/jellyfin/"
[2025-08-01 00:58:55.546 +02:00] [INF] [1] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/cache"
[2025-08-01 00:58:56.340 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Loading assemblies
[2025-08-01 00:58:56.498 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.ChapterSegments, Version=*******, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Chapter Segments Provider_*******/Jellyfin.Plugin.ChapterSegments.dll"
[2025-08-01 00:58:56.817 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.PlaybackReporting, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Playback Reporting_********/Jellyfin.Plugin.PlaybackReporting.dll"
[2025-08-01 00:58:56.819 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "SQLitePCL.pretty, Version=*******, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Playback Reporting_********/SQLitePCL.pretty.dll"
[2025-08-01 00:58:57.486 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "ClosedXML, Version=0.97.0.0, Culture=neutral, PublicKeyToken=fd1eb21b62ae805b" from "/config/plugins/Reports_********/ClosedXML.dll"
[2025-08-01 00:58:58.186 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "DocumentFormat.OpenXml, Version=2.16.0.0, Culture=neutral, PublicKeyToken=8fb06cb64d019a17" from "/config/plugins/Reports_********/DocumentFormat.OpenXml.dll"
[2025-08-01 00:58:58.205 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "ExcelNumberFormat, Version=1.1.0.0, Culture=neutral, PublicKeyToken=23c6f5d73be07eca" from "/config/plugins/Reports_********/ExcelNumberFormat.dll"
[2025-08-01 00:58:58.229 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Reports, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Reports_********/Jellyfin.Plugin.Reports.dll"
[2025-08-01 00:58:58.250 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "SixLabors.Fonts, Version=*******, Culture=neutral, PublicKeyToken=d998eea7b14cab13" from "/config/plugins/Reports_********/SixLabors.Fonts.dll"
[2025-08-01 00:58:58.412 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "System.IO.Packaging, Version=4.0.5.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" from "/config/plugins/Reports_********/System.IO.Packaging.dll"
[2025-08-01 00:58:58.950 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Tvdb, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Jellyfin.Plugin.Tvdb.dll"
[2025-08-01 00:58:59.050 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Tvdb.Sdk, Version=4.7.10.0, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Tvdb.Sdk.dll"
[2025-08-01 00:58:59.269 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Trakt, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Trakt_********/Trakt.dll"
[2025-08-01 00:58:59.281 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.TranscodeKiller, Version=*******, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Transcode Killer_*******/Jellyfin.Plugin.TranscodeKiller.dll"
[2025-08-01 00:59:00.684 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "BouncyCastle.Cryptography, Version=2.0.0.0, Culture=neutral, PublicKeyToken=072edcf4a5328938" from "/config/plugins/Webhook_********/BouncyCastle.Cryptography.dll"
[2025-08-01 00:59:00.833 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Handlebars, Version=2.1.6.0, Culture=neutral, PublicKeyToken=22225d0bf33cd661" from "/config/plugins/Webhook_********/Handlebars.dll"
[2025-08-01 00:59:00.844 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Webhook, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Webhook_********/Jellyfin.Plugin.Webhook.dll"
[2025-08-01 00:59:00.968 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "MailKit, Version=4.8.0.0, Culture=neutral, PublicKeyToken=4e064fe7c44a8f1b" from "/config/plugins/Webhook_********/MailKit.dll"
[2025-08-01 00:59:01.030 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "MimeKit, Version=4.8.0.0, Culture=neutral, PublicKeyToken=bede1c8a46c66814" from "/config/plugins/Webhook_********/MimeKit.dll"
[2025-08-01 00:59:01.052 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "MQTTnet, Version=4.3.7.1207, Culture=neutral, PublicKeyToken=fdb7629f2e364a63" from "/config/plugins/Webhook_********/MQTTnet.dll"
[2025-08-01 00:59:01.056 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "MQTTnet.Extensions.ManagedClient, Version=4.3.7.1207, Culture=neutral, PublicKeyToken=fdb7629f2e364a63" from "/config/plugins/Webhook_********/MQTTnet.Extensions.ManagedClient.dll"
[2025-08-01 00:59:01.370 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-08-01 00:59:01.376 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN exclusions: []
[2025-08-01 00:59:01.380 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Used LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-08-01 00:59:01.388 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered interface addresses: ["127.0.0.1", "**********"]
[2025-08-01 00:59:01.392 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Bind Addresses ["0.0.0.0"]
[2025-08-01 00:59:01.395 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Remote IP filter is "Allowlist"
[2025-08-01 00:59:01.397 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered subnets: []
[2025-08-01 00:59:13.931 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Chapter Segments Provider" "*******"
[2025-08-01 00:59:13.937 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Playback Reporting" "********"
[2025-08-01 00:59:13.950 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Reports" "********"
[2025-08-01 00:59:13.970 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TheTVDB" "********"
[2025-08-01 00:59:13.983 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Trakt" "********"
[2025-08-01 00:59:14.000 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Transcode Killer" "*******"
[2025-08-01 00:59:14.010 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Webhook" "********"
[2025-08-01 00:59:14.018 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TMDb" "10.10.7.0"
[2025-08-01 00:59:14.022 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Studio Images" "10.10.7.0"
[2025-08-01 00:59:14.023 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "OMDb" "10.10.7.0"
[2025-08-01 00:59:14.111 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "MusicBrainz" "10.10.7.0"
[2025-08-01 00:59:14.111 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "AudioDB" "10.10.7.0"
[2025-08-01 00:59:14.673 +02:00] [INF] [1] Main: Kestrel is listening on "0.0.0.0"
[2025-08-01 00:59:14.695 +02:00] [INF] [1] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: EventMonitorEntryPoint Running
[2025-08-01 00:59:14.721 +02:00] [INF] [1] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: Sqlite version: 3.41.2
[2025-08-01 00:59:14.724 +02:00] [INF] [1] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: Sqlite compiler options: ATOMIC_INTRINSICS=1,COMPILER=gcc-9.4.0,DEFAULT_AUTOVACUUM,DEFAULT_CACHE_SIZE=-2000,DEFAULT_FILE_FORMAT=4,DEFAULT_FOREIGN_KEYS,DEFAULT_JOURNAL_SIZE_LIMIT=-1,DEFAULT_MMAP_SIZE=0,DEFAULT_PAGE_SIZE=4096,DEFAULT_PCACHE_INITSZ=20,DEFAULT_RECURSIVE_TRIGGERS,DEFAULT_SECTOR_SIZE=4096,DEFAULT_SYNCHRONOUS=2,DEFAULT_WAL_AUTOCHECKPOINT=1000,DEFAULT_WAL_SYNCHRONOUS=2,DEFAULT_WORKER_THREADS=0,ENABLE_COLUMN_METADATA,ENABLE_FTS3,ENABLE_FTS3_PARENTHESIS,ENABLE_FTS4,ENABLE_FTS5,ENABLE_MATH_FUNCTIONS,ENABLE_RTREE,ENABLE_SNAPSHOT,MALLOC_SOFT_LIMIT=1024,MAX_ATTACHED=10,MAX_COLUMN=2000,MAX_COMPOUND_SELECT=500,MAX_DEFAULT_PAGE_SIZE=8192,MAX_EXPR_DEPTH=1000,MAX_FUNCTION_ARG=127,MAX_LENGTH=1000000000,MAX_LIKE_PATTERN_LENGTH=50000,MAX_MMAP_SIZE=0x7fff0000,MAX_PAGE_COUNT=1073741823,MAX_PAGE_SIZE=65536,MAX_SQL_LENGTH=1000000000,MAX_TRIGGER_DEPTH=1000,MAX_VARIABLE_NUMBER=32766,MAX_VDBE_OP=250000000,MAX_WORKER_THREADS=8,MUTEX_PTHREADS,SYSTEM_MALLOC,TEMP_STORE=1,THREADSAFE=1
[2025-08-01 00:59:14.756 +02:00] [INF] [1] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: Default journal_mode for "/config/data/playback_reporting.db" is "delete"
[2025-08-01 00:59:14.766 +02:00] [INF] [1] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: Initialize PlaybackActivity Repository
[2025-08-01 00:59:14.772 +02:00] [INF] [1] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: PlaybackActivity table schema OK
[2025-08-01 00:59:14.773 +02:00] [INF] [1] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: Expected : "datecreated:datetime|userid:text|itemid:text|itemtype:text|itemname:text|playbackmethod:text|clientname:text|devicename:text|playduration:int"
[2025-08-01 00:59:14.773 +02:00] [INF] [1] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: Received : "datecreated:datetime|userid:text|itemid:text|itemtype:text|itemname:text|playbackmethod:text|clientname:text|devicename:text|playduration:int"
[2025-08-01 00:59:16.752 +02:00] [WRN] [1] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: "/wwwroot". Static files may be unavailable.
[2025-08-01 00:59:16.848 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Running startup tasks
[2025-08-01 00:59:16.851 +02:00] [INF] [1] Jellyfin.Plugin.PlaybackReporting.TaskCleanDb: TaskCleanDb Loaded
[2025-08-01 00:59:16.853 +02:00] [INF] [1] Jellyfin.Plugin.PlaybackReporting.TaskRunBackup: TaskRunBackup Loaded
[2025-08-01 00:59:16.902 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Playback Reporting Trim Db" set to fire at 2025-08-02 00:00:00.000 +02:00, which is 23:00:43.0988431 from now.
[2025-08-01 00:59:16.994 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-08-01 03:00:00.000 +02:00, which is 02:00:43.0056969 from now.
[2025-08-01 00:59:17.052 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-08-01 02:00:00.000 +02:00, which is 01:00:42.9473121 from now.
[2025-08-01 00:59:17.058 +02:00] [INF] [8] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-08-01 00:59:17.348 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Found ffmpeg version "7.0.2"
[2025-08-01 00:59:17.407 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "decoders": ["libdav1d", "av1", "av1_cuvid", "av1_qsv", "h264", "h264_qsv", "h264_cuvid", "hevc", "hevc_qsv", "hevc_cuvid", "mpeg2video", "mpeg2_qsv", "mpeg2_cuvid", "mpeg4", "mpeg4_cuvid", "msmpeg4", "vc1_qsv", "vc1_cuvid", "vp8", "libvpx", "vp8_cuvid", "vp8_qsv", "vp9", "libvpx-vp9", "vp9_cuvid", "vp9_qsv", "aac", "ac3", "ac4", "dca", "flac", "mp3", "truehd"]
[2025-08-01 00:59:17.434 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "encoders": ["libsvtav1", "av1_nvenc", "av1_qsv", "av1_amf", "av1_vaapi", "libx264", "h264_amf", "h264_nvenc", "h264_qsv", "h264_v4l2m2m", "h264_vaapi", "libx265", "hevc_amf", "hevc_nvenc", "hevc_qsv", "hevc_vaapi", "mjpeg_qsv", "mjpeg_vaapi", "aac", "libfdk_aac", "ac3", "alac", "dca", "flac", "libmp3lame", "libopus", "truehd", "libvorbis", "srt"]
[2025-08-01 00:59:17.465 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available filters: ["bwdif_cuda", "deinterlace_qsv", "deinterlace_vaapi", "flip_vulkan", "hwupload_cuda", "hwupload_vaapi", "libplacebo", "overlay_opencl", "overlay_qsv", "overlay_vaapi", "overlay_vulkan", "overlay_cuda", "procamp_vaapi", "scale_cuda", "scale_opencl", "scale_qsv", "scale_vaapi", "scale_vulkan", "tonemapx", "tonemap_cuda", "tonemap_opencl", "tonemap_vaapi", "transpose_cuda", "transpose_opencl", "transpose_vaapi", "transpose_vulkan", "vpp_qsv", "yadif_cuda", "zscale", "alphasrc"]
[2025-08-01 00:59:17.651 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available hwaccel types: ["cuda", "vaapi", "qsv", "drm", "opencl", "vulkan"]
[2025-08-01 00:59:18.131 +02:00] [INF] [12] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-08-01 00:59:20.084 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar colecciones y listas de reproducción" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:59:20.152 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar las transcodificaciones" Completed after 0 minute(s) and 0 seconds
[2025-08-01 00:59:25.783 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Actualizar extensiones" Completed after 0 minute(s) and 5 seconds
[2025-08-01 00:59:28.985 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: FFmpeg: "/usr/lib/jellyfin-ffmpeg/ffmpeg"
[2025-08-01 00:59:28.996 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: ServerId: "ab3b8d0d220e4964b21db2001d353fcc"
[2025-08-01 00:59:28.996 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Core startup complete
[2025-08-01 00:59:28.996 +02:00] [INF] [1] Main: Startup complete 0:00:37.4160143
[2025-08-01 01:00:00.106 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:00:00.117 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:00:00.125 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 01:00:46.946 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:00:46.981 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:02:17.939 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:02:17.974 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:03:48.934 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:03:48.972 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:05:00.049 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:05:00.051 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:05:00.056 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 01:05:19.933 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:05:19.971 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:06:50.931 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:06:50.970 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:08:21.925 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:08:21.965 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:09:52.922 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:09:52.962 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:10:00.050 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:10:00.051 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:10:00.051 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 01:11:23.917 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:11:23.959 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:12:54.915 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:12:54.956 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:14:25.918 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:14:25.953 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:15:00.034 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:15:00.034 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:15:00.034 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 01:15:56.915 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:15:56.951 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:17:27.911 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:17:27.947 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:18:58.912 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:18:58.946 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:20:00.017 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:20:00.017 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:20:00.017 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 01:20:29.906 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:20:29.944 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:22:00.908 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:22:00.943 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:23:31.904 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:23:31.942 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:25:00.059 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:25:00.060 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:25:00.060 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 01:25:02.903 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:25:02.941 +02:00] [INF] [26] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:26:33.899 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:26:33.937 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:28:04.896 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:28:04.935 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:29:35.896 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:29:35.934 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:30:00.029 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:30:00.029 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:30:00.030 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 01:31:06.893 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:31:06.931 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:32:37.889 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:32:37.927 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:34:08.887 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:34:08.928 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:35:00.063 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:35:00.064 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:35:00.064 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 01:35:39.885 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:35:39.931 +02:00] [INF] [25] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:37:10.887 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:37:10.934 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:38:41.885 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:38:41.931 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:40:00.028 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:40:00.028 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:40:00.028 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 01:40:12.884 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:40:12.930 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:41:43.884 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:41:43.927 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:43:14.884 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:43:14.929 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:44:45.878 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:44:45.928 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:45:00.029 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:45:00.029 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:45:00.030 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 01:46:16.879 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:46:16.927 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:47:47.877 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:47:47.929 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:49:18.874 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:49:18.925 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:50:00.023 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:50:00.023 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:50:00.023 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 01:50:49.870 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:50:49.922 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:52:20.869 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:52:20.920 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:53:51.869 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:53:51.920 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:55:00.022 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:55:00.022 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 01:55:00.022 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 01:55:22.866 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:55:22.920 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:56:53.866 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:56:53.917 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:58:24.861 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:58:24.914 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:59:55.859 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 01:59:55.914 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:00:00.036 +02:00] [INF] [18] Emby.Server.Implementations.MediaEncoder.EncodingManager: Skipping chapter image extraction for "Capitán América: Brave New World" as the average chapter duration 0 was lower than the minimum threshold 10000000
[2025-08-01 02:00:00.072 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:00:00.073 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:00:00.073 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 02:00:00.183 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Extraer imágenes de los capítulos" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:00:00.842 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-08-02 02:00:00.000 +02:00, which is 23:59:59.1572048 from now.
[2025-08-01 02:01:26.859 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:01:26.911 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:02:57.856 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:02:57.910 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:04:28.859 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:04:28.906 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:05:00.021 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:05:00.021 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:05:00.021 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 02:05:59.857 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:05:59.905 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:07:30.851 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:07:30.899 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:09:01.854 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:09:01.906 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:10:00.022 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:10:00.022 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:10:00.022 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 02:10:32.846 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:10:32.904 +02:00] [INF] [25] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:12:03.845 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:12:03.901 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:13:34.843 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:13:34.904 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:15:00.047 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:15:00.052 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:15:00.052 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 02:15:05.843 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:15:05.902 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:16:36.838 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:16:36.898 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:18:07.837 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:18:07.896 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:19:38.838 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:19:38.898 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:20:00.035 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:20:00.035 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:20:00.035 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 02:21:09.842 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:21:09.898 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:22:40.837 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:22:40.893 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:24:11.835 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:24:11.894 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:25:00.023 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:25:00.023 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:25:00.023 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 02:25:42.831 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:25:42.895 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:27:13.832 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:27:13.894 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:28:44.829 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:28:44.897 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:30:00.056 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:30:00.057 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:30:00.057 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 02:30:15.825 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:30:15.892 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:31:46.827 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:31:46.890 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:33:17.829 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:33:17.888 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:34:48.827 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:34:48.886 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:35:00.042 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:35:00.042 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:35:00.042 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 02:36:19.827 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:36:19.882 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:37:50.822 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:37:50.880 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:39:21.824 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:39:21.884 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:40:00.043 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:40:00.043 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:40:00.045 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 02:40:52.822 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:40:52.885 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:42:23.820 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:42:23.879 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:43:54.820 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:43:54.878 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:45:00.023 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:45:00.023 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:45:00.024 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 02:45:25.817 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:45:25.877 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:46:56.811 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:46:56.877 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:48:27.810 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:48:27.877 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:49:58.806 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:49:58.879 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:50:00.067 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:50:00.067 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:50:00.068 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 02:51:29.806 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:51:29.878 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:53:00.806 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:53:00.878 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:54:31.800 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:54:31.873 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:55:00.037 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:55:00.037 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 02:55:00.037 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 02:56:02.796 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:56:02.874 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:57:33.798 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:57:33.873 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:59:04.799 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:59:04.871 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 02:59:59.830 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Generar miniaturas de línea de tiempo" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:00:00.086 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:00:00.088 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:00:00.088 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 03:00:00.088 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:00:00.088 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:00:00.089 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 03:00:00.699 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-08-02 03:00:00.000 +02:00, which is 23:59:59.3006521 from now.
[2025-08-01 03:00:35.796 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:00:35.867 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:02:06.793 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:02:06.864 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:03:37.795 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:03:37.863 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:05:00.047 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:05:00.047 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:05:00.047 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 03:05:08.791 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:05:08.858 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:06:39.791 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:06:39.857 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:08:10.790 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:08:10.856 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:09:41.790 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:09:41.857 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:10:00.042 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:10:00.042 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:10:00.042 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 03:11:12.785 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:11:12.852 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:12:43.782 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:12:43.850 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:14:14.779 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:14:14.847 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:15:00.044 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:15:00.044 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:15:00.044 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 03:15:45.780 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:15:45.846 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:17:16.783 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:17:16.844 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:18:47.784 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:18:47.844 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:20:00.051 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:20:00.051 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:20:00.051 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 03:20:18.778 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:20:18.844 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:21:49.777 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:21:49.840 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:23:20.777 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:23:20.839 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:24:51.781 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:24:51.840 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:25:00.034 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:25:00.036 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:25:00.037 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 03:26:22.773 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:26:22.833 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:27:53.771 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:27:53.839 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:29:24.773 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:29:24.833 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:30:00.039 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:30:00.039 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:30:00.040 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 03:30:55.771 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:30:55.836 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:32:26.773 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:32:26.833 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:33:57.772 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:33:57.837 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:35:00.040 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:35:00.040 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:35:00.040 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 03:35:28.770 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:35:28.833 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:36:59.767 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:36:59.831 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:38:30.769 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:38:30.828 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:40:00.044 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:40:00.044 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:40:00.044 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 03:40:01.769 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:40:01.830 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:41:32.763 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:41:32.825 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:43:03.761 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:43:03.827 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:44:34.762 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:44:34.821 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:45:00.023 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:45:00.023 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:45:00.024 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 03:46:05.760 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:46:05.824 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:47:36.761 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:47:36.825 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:49:07.765 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:49:07.824 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:50:00.047 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:50:00.047 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:50:00.047 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 03:50:38.762 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:50:38.821 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:52:09.759 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:52:09.819 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:53:40.761 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:53:40.820 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:55:00.042 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:55:00.043 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 03:55:00.043 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 03:55:11.754 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:55:11.820 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:56:42.754 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:56:42.819 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:58:13.752 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:58:13.817 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:59:44.752 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 03:59:44.815 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:00:00.050 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:00:00.051 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:00:00.051 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 04:01:15.753 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:01:15.821 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:02:46.749 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:02:46.819 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:04:17.743 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:04:17.822 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:05:00.032 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:05:00.034 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:05:00.035 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 04:05:48.741 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:05:48.817 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:07:19.741 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:07:19.816 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:08:50.738 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:08:50.813 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:10:00.042 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:10:00.043 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:10:00.043 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 04:10:21.735 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:10:21.813 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:11:52.738 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:11:52.813 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:13:23.729 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:13:23.807 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:14:54.730 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:14:54.804 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:15:00.042 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:15:00.042 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:15:00.042 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 04:16:25.726 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:16:25.807 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:17:56.733 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:17:56.807 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:19:27.733 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:19:27.804 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:20:00.037 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:20:00.037 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:20:00.038 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 04:20:58.727 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:20:58.798 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:22:29.724 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:22:29.801 +02:00] [INF] [25] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:24:00.724 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:24:00.795 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:25:00.049 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:25:00.050 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:25:00.050 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 04:25:31.720 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:25:31.794 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:27:02.717 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:27:02.788 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:28:33.716 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:28:33.791 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:30:00.037 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:30:00.037 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:30:00.037 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 04:30:04.713 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:30:04.793 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:31:35.714 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:31:35.794 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:33:06.713 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:33:06.793 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:34:37.708 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:34:37.788 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:35:00.039 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:35:00.040 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:35:00.040 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 04:36:08.708 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:36:08.788 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:37:39.706 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:37:39.789 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:39:10.711 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:39:10.790 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:40:00.062 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:40:00.063 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:40:00.064 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 04:40:41.704 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:40:41.788 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:42:12.703 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:42:12.787 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:43:43.704 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:43:43.787 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:45:00.021 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:45:00.021 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:45:00.022 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 04:45:14.699 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:45:14.782 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:46:45.701 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:46:45.780 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:48:16.700 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:48:16.783 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:49:47.697 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:49:47.781 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:50:00.040 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:50:00.040 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:50:00.040 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 04:51:18.700 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:51:18.783 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:52:49.693 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:52:49.780 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:54:20.691 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:54:20.780 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:55:00.041 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:55:00.042 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 04:55:00.042 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 04:55:51.695 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:55:51.775 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:57:22.699 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:57:22.777 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:58:53.693 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 04:58:53.773 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:00:00.055 +02:00] [INF] [23] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:00:00.057 +02:00] [INF] [23] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:00:00.057 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:00:00.058 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:00:00.059 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 05:00:00.061 +02:00] [INF] [23] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 05:00:24.691 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:00:24.770 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:01:55.689 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:01:55.772 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:03:26.691 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:03:26.775 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:04:57.694 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:04:57.773 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:05:00.053 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:05:00.054 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:05:00.054 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 05:06:28.692 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:06:28.774 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:07:59.690 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:07:59.773 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:09:30.691 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:09:30.773 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:10:00.042 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:10:00.042 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:10:00.042 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 05:11:01.688 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:11:01.775 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:12:32.685 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:12:32.775 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:14:03.681 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:14:03.775 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:15:00.037 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:15:00.037 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:15:00.037 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 05:15:34.678 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:15:34.773 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:17:05.676 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:17:05.773 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:18:36.673 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:18:36.775 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:20:00.050 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:20:00.050 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:20:00.050 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 05:20:07.676 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:20:07.775 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:21:38.675 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:21:38.775 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:23:09.671 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:23:09.770 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:24:40.673 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:24:40.772 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:25:00.058 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:25:00.059 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:25:00.060 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 05:26:11.667 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:26:11.769 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:27:42.666 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:27:42.767 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:29:13.666 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:29:13.768 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:30:00.064 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:30:00.065 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:30:00.065 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 05:30:44.662 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:30:44.763 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:32:15.662 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:32:15.765 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:33:46.661 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:33:46.763 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:35:00.047 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:35:00.048 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:35:00.048 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 05:35:17.663 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:35:17.761 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:36:48.661 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:36:48.761 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:38:19.659 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:38:19.758 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:39:50.661 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:39:50.760 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:40:00.046 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:40:00.046 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:40:00.046 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 05:41:21.660 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:41:21.759 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:42:52.658 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:42:52.757 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:44:23.655 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:44:23.756 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:45:00.019 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:45:00.019 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:45:00.020 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 05:45:54.655 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:45:54.753 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:47:25.654 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:47:25.754 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:48:56.655 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:48:56.751 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:50:00.051 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:50:00.051 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:50:00.051 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 05:50:27.655 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:50:27.750 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:51:58.650 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:51:58.747 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:53:29.653 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:53:29.744 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:55:00.041 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:55:00.041 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 05:55:00.041 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 05:55:00.653 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:55:00.746 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:56:31.652 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:56:31.743 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:58:02.650 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:58:02.737 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:59:33.646 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 05:59:33.737 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:00:00.042 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:00:00.042 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:00:00.042 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 06:01:04.650 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:01:04.737 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:02:35.641 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:02:35.736 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:04:06.647 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:04:06.735 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:05:00.051 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:05:00.051 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:05:00.051 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 06:05:37.641 +02:00] [INF] [25] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:05:37.731 +02:00] [INF] [25] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:07:08.640 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:07:08.731 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:08:39.642 +02:00] [INF] [25] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:08:39.733 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:10:00.046 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:10:00.047 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:10:00.048 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 06:10:10.638 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:10:10.734 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:11:41.634 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:11:41.732 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:13:12.633 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:13:12.732 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:14:43.631 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:14:43.730 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:15:00.035 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:15:00.036 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:15:00.036 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 06:16:14.630 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:16:14.728 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:17:45.635 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:17:45.728 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:19:16.634 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:19:16.725 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:20:00.036 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:20:00.036 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:20:00.036 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 06:20:47.630 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:20:47.722 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:22:18.630 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:22:18.722 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:23:49.628 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:23:49.724 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:25:00.043 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:25:00.043 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:25:00.043 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 06:25:20.625 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:25:20.728 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:26:51.627 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:26:51.723 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:28:22.629 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:28:22.723 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:29:53.632 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:29:53.720 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:30:00.039 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:30:00.040 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:30:00.040 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 06:31:24.635 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:31:24.718 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:32:55.632 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:32:55.717 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:34:26.632 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:34:26.716 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:35:00.036 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:35:00.037 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:35:00.037 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 06:35:57.632 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:35:57.716 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:37:28.628 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:37:28.711 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:38:59.627 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:38:59.711 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:40:00.032 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:40:00.032 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:40:00.032 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 06:40:30.626 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:40:30.713 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:42:01.624 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:42:01.712 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:43:32.622 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:43:32.712 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:45:00.038 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:45:00.038 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:45:00.038 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 06:45:03.618 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:45:03.708 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:46:34.616 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:46:34.703 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:48:05.615 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:48:05.703 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:49:36.615 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:49:36.706 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:50:00.032 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:50:00.032 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:50:00.033 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 06:51:07.612 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:51:07.703 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:52:38.609 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:52:38.701 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:54:09.608 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:54:09.704 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:55:00.034 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:55:00.034 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 06:55:00.035 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 06:55:40.607 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:55:40.705 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:57:11.602 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:57:11.700 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:58:42.596 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 06:58:42.702 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:00:00.036 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:00:00.037 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:00:00.037 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 07:00:13.598 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:00:13.700 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:01:44.597 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:01:44.697 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:03:15.592 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:03:15.694 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:04:46.590 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:04:46.692 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:05:00.038 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:05:00.039 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:05:00.039 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 07:06:17.590 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:06:17.688 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:07:48.590 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:07:48.690 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:09:19.588 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:09:19.686 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:10:00.037 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:10:00.039 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:10:00.039 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 07:10:50.586 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:10:50.682 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:12:21.585 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:12:21.680 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:13:52.582 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:13:52.677 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:15:00.034 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:15:00.034 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:15:00.034 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 07:15:23.582 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:15:23.677 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:16:54.581 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:16:54.672 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:18:25.581 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:18:25.668 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:19:56.581 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:19:56.668 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:20:00.036 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:20:00.036 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:20:00.036 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 07:21:27.579 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:21:27.664 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:22:58.583 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:22:58.663 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:24:29.580 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:24:29.660 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:25:00.041 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:25:00.041 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:25:00.041 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 07:26:00.578 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:26:00.662 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:27:31.580 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:27:31.659 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:29:02.577 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:29:02.661 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:30:00.032 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:30:00.032 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:30:00.033 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 07:30:33.573 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:30:33.658 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:32:04.574 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:32:04.656 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:33:35.570 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:33:35.653 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:35:00.034 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:35:00.034 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:35:00.034 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 07:35:06.567 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:35:06.650 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:36:37.568 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:36:37.648 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:38:08.567 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:38:08.647 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:39:39.563 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:39:39.645 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:40:00.035 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:40:00.035 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:40:00.035 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 07:41:10.562 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:41:10.641 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:42:41.557 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:42:41.637 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:44:12.554 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:44:12.634 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:45:00.043 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:45:00.043 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:45:00.043 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 07:45:43.553 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:45:43.635 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:47:14.550 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:47:14.636 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:48:45.552 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:48:45.631 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:50:00.037 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:50:00.037 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:50:00.037 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 07:50:16.554 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:50:16.630 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:51:47.548 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:51:47.627 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:53:18.546 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:53:18.621 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:54:49.547 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:54:49.619 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:55:00.038 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:55:00.038 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 07:55:00.038 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 07:56:20.549 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:56:20.624 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:57:51.542 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:57:51.618 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:59:22.542 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 07:59:22.618 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:00:00.036 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:00:00.036 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:00:00.037 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 08:00:53.537 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:00:53.616 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:02:24.539 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:02:24.614 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:03:55.535 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:03:55.610 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:05:00.033 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:05:00.033 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:05:00.033 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 08:05:26.533 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:05:26.612 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:06:57.538 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:06:57.608 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:08:28.535 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:08:28.607 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:09:59.532 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:09:59.609 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:10:00.040 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:10:00.040 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:10:00.040 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 08:11:30.528 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:11:30.609 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:13:01.528 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:13:01.607 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:14:32.525 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:14:32.604 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:15:00.039 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:15:00.039 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:15:00.039 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 08:16:03.527 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:16:03.606 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:17:34.525 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:17:34.607 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:19:05.525 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:19:05.604 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:20:00.039 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:20:00.039 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:20:00.039 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 08:20:36.522 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:20:36.606 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:22:07.521 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:22:07.604 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:23:38.518 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:23:38.605 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:25:00.034 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:25:00.034 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:25:00.035 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 08:25:09.520 +02:00] [INF] [3] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:25:09.606 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:26:40.516 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:26:40.606 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:28:11.519 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:28:11.602 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:29:42.515 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:29:42.598 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:30:00.037 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:30:00.037 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:30:00.037 +02:00] [INF] [10] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 08:31:13.518 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:31:13.600 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:32:44.515 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:32:44.595 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:34:15.516 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:34:15.595 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:35:00.043 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:35:00.043 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:35:00.043 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 08:35:46.514 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:35:46.593 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:37:17.510 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:37:17.590 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:38:48.513 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:38:48.593 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:40:00.032 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:40:00.032 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:40:00.033 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 08:40:19.510 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:40:19.589 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:41:50.510 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:41:50.586 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:43:21.508 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:43:21.583 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:44:52.510 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:44:52.585 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:45:00.051 +02:00] [INF] [23] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:45:00.052 +02:00] [INF] [23] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:45:00.052 +02:00] [INF] [23] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 08:46:23.504 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:46:23.582 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:47:54.506 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:47:54.584 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:49:25.500 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:49:25.584 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:50:00.034 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:50:00.034 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:50:00.034 +02:00] [INF] [22] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 08:50:56.500 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:50:56.587 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:52:27.499 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:52:27.583 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:53:58.498 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:53:58.584 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:55:00.087 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:55:00.087 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 08:55:00.088 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 08:55:29.499 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:55:29.582 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:57:00.496 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:57:00.579 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:58:31.491 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 08:58:31.578 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:00:00.043 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:00:00.043 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:00:00.043 +02:00] [INF] [17] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 09:00:02.495 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:00:02.577 +02:00] [INF] [25] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:01:33.494 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:01:33.573 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:03:04.491 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:03:04.572 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:04:35.492 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:04:35.569 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:05:00.053 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:05:00.054 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:05:00.054 +02:00] [INF] [19] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 09:06:06.493 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:06:06.569 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:07:37.490 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:07:37.565 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:09:08.490 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:09:08.564 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:10:00.052 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:10:00.052 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:10:00.053 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 09:10:39.486 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:10:39.561 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:12:10.485 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:12:10.561 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:13:41.482 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:13:41.558 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:15:00.046 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:15:00.046 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:15:00.046 +02:00] [INF] [21] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 09:15:12.475 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:15:12.558 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:16:43.475 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:16:43.555 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:18:14.471 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:18:14.551 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:19:45.472 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:19:45.551 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:20:00.059 +02:00] [INF] [23] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:20:00.059 +02:00] [INF] [23] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:20:00.059 +02:00] [INF] [23] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 09:21:16.473 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:21:16.548 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:22:47.469 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:22:47.545 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:24:18.470 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:24:18.544 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:25:00.065 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:25:00.065 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:25:00.066 +02:00] [INF] [3] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 09:25:49.472 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:25:49.543 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:27:20.470 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:27:20.549 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:28:51.466 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:28:51.545 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:30:00.065 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:30:00.065 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:30:00.065 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 09:30:22.465 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:30:22.544 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:31:53.466 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:31:53.544 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:33:24.461 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:33:24.541 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:34:55.459 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:34:55.539 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:35:00.061 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:35:00.061 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:35:00.061 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 09:36:26.455 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:36:26.535 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:37:57.452 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:37:57.537 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:39:28.452 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:39:28.534 +02:00] [INF] [24] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:40:00.121 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:40:00.121 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:40:00.121 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 09:40:59.454 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:40:59.534 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:42:30.449 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:42:30.531 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:44:01.449 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:44:01.528 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:45:00.037 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:45:00.038 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:45:00.038 +02:00] [INF] [24] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 09:45:32.449 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:45:32.528 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:47:03.449 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:47:03.525 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:48:34.448 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:48:34.522 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:50:00.045 +02:00] [INF] [23] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:50:00.045 +02:00] [INF] [23] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:50:00.045 +02:00] [INF] [23] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 09:50:05.445 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:50:05.523 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:51:36.446 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:51:36.521 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:53:07.444 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:53:07.519 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:54:38.443 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:54:38.517 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:55:00.049 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:55:00.049 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 09:55:00.049 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 09:56:09.439 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:56:09.515 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:57:40.436 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:57:40.538 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:59:11.437 +02:00] [INF] [21] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 09:59:11.536 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:00:00.081 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 10:00:00.081 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 10:00:00.082 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 10:00:42.437 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:00:42.537 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:02:13.438 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:02:13.537 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:03:44.438 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:03:44.538 +02:00] [INF] [23] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:05:00.077 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 10:05:00.079 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 10:05:00.080 +02:00] [INF] [18] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 10:05:15.434 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:05:15.535 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:06:46.433 +02:00] [INF] [22] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:06:46.533 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:08:17.431 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:08:17.534 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:09:48.433 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:09:48.531 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:10:00.035 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 10:10:00.035 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 10:10:00.035 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 10:11:19.431 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:11:19.530 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:12:50.429 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:12:50.525 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:14:21.438 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:14:21.528 +02:00] [INF] [17] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:15:00.338 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 10:15:00.338 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 10:15:00.339 +02:00] [INF] [12] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 10:15:52.438 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:15:52.529 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:17:23.491 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:17:24.702 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:18:55.223 +02:00] [INF] [19] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:18:55.778 +02:00] [INF] [18] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:20:00.274 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 10:20:00.274 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 10:20:00.274 +02:00] [INF] [20] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 10:20:26.203 +02:00] [INF] [20] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Deleted Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:20:26.762 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Webhook Item Added Notifier" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:24:17.934 +02:00] [INF] [1] Main: Jellyfin version: "10.10.7"
[2025-08-01 10:24:18.339 +02:00] [INF] [1] Main: Environment Variables: ["[JELLYFIN_CONFIG_DIR, /config/config]", "[JELLYFIN_DATA_DIR, /config]", "[JELLYFIN_FFMPEG, /usr/lib/jellyfin-ffmpeg/ffmpeg]", "[JELLYFIN_LOG_DIR, /config/log]", "[JELLYFIN_WEB_DIR, /jellyfin/jellyfin-web]", "[JELLYFIN_CACHE_DIR, /cache]"]
[2025-08-01 10:24:18.342 +02:00] [INF] [1] Main: Arguments: ["/jellyfin/jellyfin.dll"]
[2025-08-01 10:24:18.371 +02:00] [INF] [1] Main: Operating system: "Debian GNU/Linux 12 (bookworm)"
[2025-08-01 10:24:18.371 +02:00] [INF] [1] Main: Architecture: X64
[2025-08-01 10:24:18.372 +02:00] [INF] [1] Main: 64-Bit Process: True
[2025-08-01 10:24:18.372 +02:00] [INF] [1] Main: User Interactive: True
[2025-08-01 10:24:18.372 +02:00] [INF] [1] Main: Processor count: 2
[2025-08-01 10:24:18.372 +02:00] [INF] [1] Main: Program data path: "/config"
[2025-08-01 10:24:18.372 +02:00] [INF] [1] Main: Log directory path: "/config/log"
[2025-08-01 10:24:18.372 +02:00] [INF] [1] Main: Config directory path: "/config/config"
[2025-08-01 10:24:18.373 +02:00] [INF] [1] Main: Cache path: "/cache"
[2025-08-01 10:24:18.373 +02:00] [INF] [1] Main: Temp directory path: "/tmp/jellyfin"
[2025-08-01 10:24:18.373 +02:00] [INF] [1] Main: Web resources path: "/jellyfin/jellyfin-web"
[2025-08-01 10:24:18.373 +02:00] [INF] [1] Main: Application directory: "/jellyfin/"
[2025-08-01 10:24:20.185 +02:00] [INF] [1] Emby.Server.Implementations.AppBase.BaseConfigurationManager: Setting cache path: "/cache"
[2025-08-01 10:24:20.752 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Loading assemblies
[2025-08-01 10:24:20.908 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.ChapterSegments, Version=*******, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Chapter Segments Provider_*******/Jellyfin.Plugin.ChapterSegments.dll"
[2025-08-01 10:24:21.365 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.PlaybackReporting, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Playback Reporting_********/Jellyfin.Plugin.PlaybackReporting.dll"
[2025-08-01 10:24:21.405 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "SQLitePCL.pretty, Version=*******, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Playback Reporting_********/SQLitePCL.pretty.dll"
[2025-08-01 10:24:22.752 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "ClosedXML, Version=0.97.0.0, Culture=neutral, PublicKeyToken=fd1eb21b62ae805b" from "/config/plugins/Reports_********/ClosedXML.dll"
[2025-08-01 10:24:23.311 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "DocumentFormat.OpenXml, Version=2.16.0.0, Culture=neutral, PublicKeyToken=8fb06cb64d019a17" from "/config/plugins/Reports_********/DocumentFormat.OpenXml.dll"
[2025-08-01 10:24:23.324 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "ExcelNumberFormat, Version=1.1.0.0, Culture=neutral, PublicKeyToken=23c6f5d73be07eca" from "/config/plugins/Reports_********/ExcelNumberFormat.dll"
[2025-08-01 10:24:23.332 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Reports, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Reports_********/Jellyfin.Plugin.Reports.dll"
[2025-08-01 10:24:23.365 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "SixLabors.Fonts, Version=*******, Culture=neutral, PublicKeyToken=d998eea7b14cab13" from "/config/plugins/Reports_********/SixLabors.Fonts.dll"
[2025-08-01 10:24:23.374 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "System.IO.Packaging, Version=4.0.5.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" from "/config/plugins/Reports_********/System.IO.Packaging.dll"
[2025-08-01 10:24:23.610 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Tvdb, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Jellyfin.Plugin.Tvdb.dll"
[2025-08-01 10:24:23.688 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Tvdb.Sdk, Version=4.7.10.0, Culture=neutral, PublicKeyToken=null" from "/config/plugins/TheTVDB_********/Tvdb.Sdk.dll"
[2025-08-01 10:24:23.844 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Trakt, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Trakt_********/Trakt.dll"
[2025-08-01 10:24:23.881 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.TranscodeKiller, Version=*******, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Transcode Killer_*******/Jellyfin.Plugin.TranscodeKiller.dll"
[2025-08-01 10:24:25.162 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "BouncyCastle.Cryptography, Version=2.0.0.0, Culture=neutral, PublicKeyToken=072edcf4a5328938" from "/config/plugins/Webhook_********/BouncyCastle.Cryptography.dll"
[2025-08-01 10:24:25.229 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Handlebars, Version=2.1.6.0, Culture=neutral, PublicKeyToken=22225d0bf33cd661" from "/config/plugins/Webhook_********/Handlebars.dll"
[2025-08-01 10:24:25.240 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "Jellyfin.Plugin.Webhook, Version=********, Culture=neutral, PublicKeyToken=null" from "/config/plugins/Webhook_********/Jellyfin.Plugin.Webhook.dll"
[2025-08-01 10:24:25.442 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "MailKit, Version=4.8.0.0, Culture=neutral, PublicKeyToken=4e064fe7c44a8f1b" from "/config/plugins/Webhook_********/MailKit.dll"
[2025-08-01 10:24:25.545 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "MimeKit, Version=4.8.0.0, Culture=neutral, PublicKeyToken=bede1c8a46c66814" from "/config/plugins/Webhook_********/MimeKit.dll"
[2025-08-01 10:24:25.603 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "MQTTnet, Version=4.3.7.1207, Culture=neutral, PublicKeyToken=fdb7629f2e364a63" from "/config/plugins/Webhook_********/MQTTnet.dll"
[2025-08-01 10:24:25.615 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded assembly "MQTTnet.Extensions.ManagedClient, Version=4.3.7.1207, Culture=neutral, PublicKeyToken=fdb7629f2e364a63" from "/config/plugins/Webhook_********/MQTTnet.Extensions.ManagedClient.dll"
[2025-08-01 10:24:26.097 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-08-01 10:24:26.098 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Defined LAN exclusions: []
[2025-08-01 10:24:26.102 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Used LAN subnets: ["127.0.0.1/8", "10.0.0.0/8", "**********/12", "***********/16"]
[2025-08-01 10:24:26.112 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered interface addresses: ["127.0.0.1", "**********"]
[2025-08-01 10:24:26.116 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Bind Addresses ["0.0.0.0"]
[2025-08-01 10:24:26.117 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Remote IP filter is "Allowlist"
[2025-08-01 10:24:26.118 +02:00] [INF] [1] Jellyfin.Networking.Manager.NetworkManager: Filtered subnets: []
[2025-08-01 10:24:50.994 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Chapter Segments Provider" "*******"
[2025-08-01 10:24:51.004 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Playback Reporting" "********"
[2025-08-01 10:24:51.011 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Reports" "********"
[2025-08-01 10:24:51.016 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TheTVDB" "********"
[2025-08-01 10:24:51.023 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Trakt" "********"
[2025-08-01 10:24:51.029 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Transcode Killer" "*******"
[2025-08-01 10:24:51.035 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Webhook" "********"
[2025-08-01 10:24:51.037 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "TMDb" "10.10.7.0"
[2025-08-01 10:24:51.038 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "Studio Images" "10.10.7.0"
[2025-08-01 10:24:51.038 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "OMDb" "10.10.7.0"
[2025-08-01 10:24:51.086 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "MusicBrainz" "10.10.7.0"
[2025-08-01 10:24:51.087 +02:00] [INF] [1] Emby.Server.Implementations.Plugins.PluginManager: Loaded plugin: "AudioDB" "10.10.7.0"
[2025-08-01 10:24:51.347 +02:00] [INF] [1] Main: Kestrel is listening on "0.0.0.0"
[2025-08-01 10:24:51.362 +02:00] [INF] [1] Jellyfin.Plugin.PlaybackReporting.EventMonitorEntryPoint: EventMonitorEntryPoint Running
[2025-08-01 10:24:51.372 +02:00] [INF] [1] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: Sqlite version: 3.41.2
[2025-08-01 10:24:51.374 +02:00] [INF] [1] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: Sqlite compiler options: ATOMIC_INTRINSICS=1,COMPILER=gcc-9.4.0,DEFAULT_AUTOVACUUM,DEFAULT_CACHE_SIZE=-2000,DEFAULT_FILE_FORMAT=4,DEFAULT_FOREIGN_KEYS,DEFAULT_JOURNAL_SIZE_LIMIT=-1,DEFAULT_MMAP_SIZE=0,DEFAULT_PAGE_SIZE=4096,DEFAULT_PCACHE_INITSZ=20,DEFAULT_RECURSIVE_TRIGGERS,DEFAULT_SECTOR_SIZE=4096,DEFAULT_SYNCHRONOUS=2,DEFAULT_WAL_AUTOCHECKPOINT=1000,DEFAULT_WAL_SYNCHRONOUS=2,DEFAULT_WORKER_THREADS=0,ENABLE_COLUMN_METADATA,ENABLE_FTS3,ENABLE_FTS3_PARENTHESIS,ENABLE_FTS4,ENABLE_FTS5,ENABLE_MATH_FUNCTIONS,ENABLE_RTREE,ENABLE_SNAPSHOT,MALLOC_SOFT_LIMIT=1024,MAX_ATTACHED=10,MAX_COLUMN=2000,MAX_COMPOUND_SELECT=500,MAX_DEFAULT_PAGE_SIZE=8192,MAX_EXPR_DEPTH=1000,MAX_FUNCTION_ARG=127,MAX_LENGTH=1000000000,MAX_LIKE_PATTERN_LENGTH=50000,MAX_MMAP_SIZE=0x7fff0000,MAX_PAGE_COUNT=1073741823,MAX_PAGE_SIZE=65536,MAX_SQL_LENGTH=1000000000,MAX_TRIGGER_DEPTH=1000,MAX_VARIABLE_NUMBER=32766,MAX_VDBE_OP=250000000,MAX_WORKER_THREADS=8,MUTEX_PTHREADS,SYSTEM_MALLOC,TEMP_STORE=1,THREADSAFE=1
[2025-08-01 10:24:51.403 +02:00] [INF] [1] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: Default journal_mode for "/config/data/playback_reporting.db" is "delete"
[2025-08-01 10:24:51.403 +02:00] [INF] [1] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: Initialize PlaybackActivity Repository
[2025-08-01 10:24:51.408 +02:00] [INF] [1] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: PlaybackActivity table schema OK
[2025-08-01 10:24:51.408 +02:00] [INF] [1] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: Expected : "datecreated:datetime|userid:text|itemid:text|itemtype:text|itemname:text|playbackmethod:text|clientname:text|devicename:text|playduration:int"
[2025-08-01 10:24:51.408 +02:00] [INF] [1] Jellyfin.Plugin.PlaybackReporting.Data.ActivityRepository: Received : "datecreated:datetime|userid:text|itemid:text|itemtype:text|itemname:text|playbackmethod:text|clientname:text|devicename:text|playduration:int"
[2025-08-01 10:24:52.729 +02:00] [WRN] [1] Microsoft.AspNetCore.StaticFiles.StaticFileMiddleware: The WebRootPath was not found: "/wwwroot". Static files may be unavailable.
[2025-08-01 10:24:52.808 +02:00] [INF] [1] Emby.Server.Implementations.ApplicationHost: Running startup tasks
[2025-08-01 10:24:52.811 +02:00] [INF] [1] Jellyfin.Plugin.PlaybackReporting.TaskCleanDb: TaskCleanDb Loaded
[2025-08-01 10:24:52.812 +02:00] [INF] [1] Jellyfin.Plugin.PlaybackReporting.TaskRunBackup: TaskRunBackup Loaded
[2025-08-01 10:24:52.865 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Playback Reporting Trim Db" set to fire at 2025-08-02 00:00:00.000 +02:00, which is 13:35:07.1350029 from now.
[2025-08-01 10:24:52.965 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Generar miniaturas de línea de tiempo" set to fire at 2025-08-02 03:00:00.000 +02:00, which is 16:35:07.0346285 from now.
[2025-08-01 10:24:53.046 +02:00] [INF] [1] Emby.Server.Implementations.ScheduledTasks.TaskManager: Daily trigger for "Extraer imágenes de los capítulos" set to fire at 2025-08-02 02:00:00.000 +02:00, which is 15:35:06.9539388 from now.
[2025-08-01 10:24:53.243 +02:00] [INF] [8] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/PELIS"
[2025-08-01 10:24:53.485 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Found ffmpeg version "7.0.2"
[2025-08-01 10:24:53.587 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "decoders": ["libdav1d", "av1", "av1_cuvid", "av1_qsv", "h264", "h264_qsv", "h264_cuvid", "hevc", "hevc_qsv", "hevc_cuvid", "mpeg2video", "mpeg2_qsv", "mpeg2_cuvid", "mpeg4", "mpeg4_cuvid", "msmpeg4", "vc1_qsv", "vc1_cuvid", "vp8", "libvpx", "vp8_cuvid", "vp8_qsv", "vp9", "libvpx-vp9", "vp9_cuvid", "vp9_qsv", "aac", "ac3", "ac4", "dca", "flac", "mp3", "truehd"]
[2025-08-01 10:24:53.638 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available "encoders": ["libsvtav1", "av1_nvenc", "av1_qsv", "av1_amf", "av1_vaapi", "libx264", "h264_amf", "h264_nvenc", "h264_qsv", "h264_v4l2m2m", "h264_vaapi", "libx265", "hevc_amf", "hevc_nvenc", "hevc_qsv", "hevc_vaapi", "mjpeg_qsv", "mjpeg_vaapi", "aac", "libfdk_aac", "ac3", "alac", "dca", "flac", "libmp3lame", "libopus", "truehd", "libvorbis", "srt"]
[2025-08-01 10:24:53.676 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available filters: ["bwdif_cuda", "deinterlace_qsv", "deinterlace_vaapi", "flip_vulkan", "hwupload_cuda", "hwupload_vaapi", "libplacebo", "overlay_opencl", "overlay_qsv", "overlay_vaapi", "overlay_vulkan", "overlay_cuda", "procamp_vaapi", "scale_cuda", "scale_opencl", "scale_qsv", "scale_vaapi", "scale_vulkan", "tonemapx", "tonemap_cuda", "tonemap_opencl", "tonemap_vaapi", "transpose_cuda", "transpose_opencl", "transpose_vaapi", "transpose_vulkan", "vpp_qsv", "yadif_cuda", "zscale", "alphasrc"]
[2025-08-01 10:24:53.936 +02:00] [INF] [1] MediaBrowser.MediaEncoding.Encoder.MediaEncoder: Available hwaccel types: ["cuda", "vaapi", "qsv", "drm", "opencl", "vulkan"]
[2025-08-01 10:24:54.589 +02:00] [INF] [12] Emby.Server.Implementations.IO.LibraryMonitor: Watching directory "/CONTENIDO/SERIES"
[2025-08-01 10:24:56.120 +02:00] [INF] [8] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar colecciones y listas de reproducción" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:24:56.243 +02:00] [INF] [12] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Limpiar las transcodificaciones" Completed after 0 minute(s) and 0 seconds
[2025-08-01 10:25:00.725 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 10:25:01.548 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: "CustomAuthentication" was not authenticated. Failure message: "Invalid token."
[2025-08-01 10:25:01.608 +02:00] [INF] [8] Jellyfin.Api.Auth.CustomAuthenticationHandler: AuthenticationScheme: "CustomAuthentication" was challenged.
[2025-08-01 10:25:02.720 +02:00] [INF] [10] Emby.Server.Implementations.ScheduledTasks.TaskManager: "Actualizar extensiones" Completed after 0 minute(s) and 6 seconds
