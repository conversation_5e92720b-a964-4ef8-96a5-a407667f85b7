
services:
  # === CADDY REVERSE PROXY ===
  caddy:
    image: caddy:2-alpine
    container_name: caddy_proxy
    restart: unless-stopped
    ports:
      - "80:80"     # HTTP
      - "443:443"   # HTTPS
      - "443:443/udp" # HTTP/3 (QUIC)
      - "8081:8081" # NTFY.SH HTTPS
    volumes:
      # Monta el Caddyfile adaptado para Docker (mantiene tu configuración)
      - C:\docker\Caddyfile.host:/etc/caddy/Caddyfile:ro
      # Datos persistentes de Caddy (certificados SSL, etc.)
      - caddy_data:/data
      - caddy_config:/config
      # Monta los directorios de logs manteniendo la estructura de tu configuración original
      - C:\docker\duckdns_updater\caddy\logs:/var/log/caddy
    networks:
      - caddy_network
    depends_on:
      - duckdns_jucago705
      - duckdns_jucago706
    healthcheck:
      test: ["CMD", "caddy", "validate", "--config", "/etc/caddy/Caddyfile"]
      interval: 5m
      timeout: 10s
      retries: 3
      start_period: 30s
    # Configuración adicional para acceso al host en Windows
    extra_hosts:
      - "host.docker.internal:host-gateway"
  # --- DuckDns 1 ---
  duckdns_jucago705:
    image: lscr.io/linuxserver/duckdns:latest
    container_name: duckdns_jucago705
    restart: unless-stopped
    environment:
      - PUID=1000 # Deja esto como 1000 para Windows
      - PGID=1000 # Deja esto como 1000 para Windows
      - TZ=Europe/Madrid # Zona horaria, cámbiala si estás en otro lugar
      - TOKEN=*************-4963-bd13-fc1199f2f5d9 # <--- ¡CAMBIA ESTO!
      - DOMAINS=tanketorrent.duckdns.org, tankeflix.duckdns.org, tankesonarr.duckdns.org, tankejackett.duckdns.org, tankeeee2.duckdns.org, tankejellyseerr.duckdns.org
      - SUBDOMAINS=false # Pon 'true' si usas subdominios que no estén en la lista DOMAINS
      - LOG_FILE=true # Opcional: crea un archivo de log dentro del volumen
    volumes:
      - ./config:/config # Mapea el volumen para guardar la configuración y logs
    networks:
      - caddy_network

  # --- DuckDns 2 ---
  duckdns_jucago706:
    image: lscr.io/linuxserver/duckdns:latest
    container_name: duckdns_jucago706
    restart: unless-stopped
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Europe/Madrid
      - TOKEN=1b37bdd3-38a3-4e5f-8eea-57a3c7419c7d
      - DOMAINS=tankeguard.duckdns.org, tankeradarr.duckdns.org,
      - SUBDOMAINS=false
      - LOG_FILE=true
    volumes:
      - ./config:/config
    networks:
      - caddy_network

      

# === ADGUARD HOME DNS SERVER & AD BLOCKER ===
  adguard:
    image: adguard/adguardhome:latest
    container_name: adguard
    environment:
      - TZ=Europe/Madrid
    volumes:
      # Configuración persistente de AdGuard Home
      - C:\docker\duckdns_updater\adguard\work:/opt/adguardhome/work
      - C:\docker\duckdns_updater\adguard\conf:/opt/adguardhome/conf
    ports:
      # Puerto DNS (TCP y UDP) - Puerto estándar para DNS
      - "53:53/tcp"
      - "53:53/udp"
      # Puerto para configuración inicial (se desactiva automáticamente después del setup)
      - "3000:3000/tcp"
      # Puerto para interfaz web (después de la configuración inicial) - Cambiado para evitar conflicto con Caddy
      - "8080:80/tcp"
      # Puertos adicionales para DNS-over-HTTPS y DNS-over-TLS (opcionales) - Cambiado para evitar conflicto con Caddy
      - "8443:443/tcp"
      - "853:853/tcp"
    restart: unless-stopped
    networks:
      - caddy_network
    # Configuración adicional para acceso al host en Windows
    extra_hosts:
      - "host.docker.internal:host-gateway"
    # Privilegios necesarios para binding a puertos bajos (53, 80)
    cap_add:
      - NET_ADMIN
      - NET_RAW


# === PORTAINER DOCKER MANAGEMENT ===
  portainer:
    image: portainer/portainer-ce:latest
    container_name: portainer
    environment:
      - TZ=Europe/Madrid
      # Configuración optimizada para acceso remoto via proxy reverso
      - PORTAINER_INSECURE_COOKIE=true
      - PORTAINER_ENABLE_HOST_MANAGEMENT_FEATURES=true
      # Configuración específica para proxy reverso
      - PORTAINER_HTTP_DISABLED=false
      - PORTAINER_HTTPS_DISABLED=true
    volumes:
      # Configuración persistente de Portainer
      - C:\docker\duckdns_updater\portainer\data:/data
      # Acceso completo al socket de Docker para gestión
      - //var/run/docker.sock:/var/run/docker.sock
    ports:
      - "9000:9000"
    restart: unless-stopped
    networks:
      - caddy_network
    # Configuración adicional para acceso al host en Windows
    extra_hosts:
      - "host.docker.internal:host-gateway"


# === WATCHTOWER AUTO-UPDATER ===
  watchtower:
    image: containrrr/watchtower:latest
    container_name: watchtower
    environment:
      - TZ=Europe/Madrid
      # Configuración de logging detallado
      - WATCHTOWER_DEBUG=true
      - WATCHTOWER_LOG_LEVEL=info
      # Configuración para escribir logs en archivos
      - WATCHTOWER_LOG_FORMAT=json
      - WATCHTOWER_NOTIFICATIONS=shoutrrr
      - WATCHTOWER_NOTIFICATION_LOG_LEVEL=info
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      # Mapeo de volumen para logs persistentes en el host
      - C:\docker\duckdns_updater\watchtower\logs:/logs
    restart: unless-stopped
    networks:
      - caddy_network
    extra_hosts:
      - "host.docker.internal:host-gateway"
    labels:
      - "com.centurylinklabs.watchtower.enable=true"
    # Configuración de logging de Docker para rotación automática
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"
        compress: "true"

# === ARR-BOT TELEGRAM BOT ===
  telegram-bot:
    build: ./telegram-bot
    container_name: telegram-bot
    environment:
      - TZ=Europe/Madrid
      # Variables de entorno cargadas desde archivo .env
    env_file:
      - ./telegram-bot/.env
    volumes:
      # Logs persistentes del bot
      - C:\docker\duckdns_updater\telegram-bot\logs:/app/logs
    ports:
      - "8082:8082"  # Puerto para webhooks (evita conflicto con AdGuard 8080)
    restart: unless-stopped
    networks:
      - caddy_network
    # Configuración adicional para acceso al host en Windows
    extra_hosts:
      - "host.docker.internal:host-gateway"
    # Dependencias - esperar a que los servicios DNS estén listos
    depends_on:
      - caddy
    # Etiquetas para Watchtower
    labels:
      - "com.centurylinklabs.watchtower.enable=true"
    # Health check para monitoreo
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8082/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
      
# === HOMEPAGE DASHBOARD ===
# homepage:
#   image: ghcr.io/gethomepage/homepage:latest
#   container_name: homepage
#   environment:
#     - PUID=1000
#     - PGID=1000
#     - TZ=Europe/Madrid
#     - HOMEPAGE_ALLOWED_HOSTS=tankeeee2.duckdns.org,localhost,localhost:3001,host.docker.internal:3001
#   volumes:
#     - C:\docker\duckdns_updater\homepage\config:/app/config
#     # Socket de Docker para los widgets de estado de contenedores
#     - /var/run/docker.sock:/var/run/docker.sock:ro
#     # Montajes de discos para monitoreo de almacenamiento
#     - /c:/mnt/c:ro
#     - /d:/mnt/d:ro
#     - /e:/mnt/e:ro
#     - /f:/mnt/f:ro
#   ports:
#     - "3001:3000"
#   networks:
#     - caddy_network
#     - arr_net
#   restart: unless-stopped
#   # Configuración adicional para acceso a Docker
#   user: "1000:999"  # Grupo docker en la mayoría de sistemas
#   privileged: false

# === VOLÚMENES ===
volumes:
  caddy_data:
    driver: local
  caddy_config:
    driver: local


# === REDES ===
networks:
  caddy_network:
    driver: bridge
    name: caddy_network
  arr_net:
    external: true #