2025-07-30 09:20:24.3|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 09:21:25.8|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 09:36:46.8|Info|RssSyncService|Starting RSS Sync
2025-07-30 09:36:46.9|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 07:20:18 and 07/30/2025 07:20:18 UTC. Search may be required.
2025-07-30 09:36:46.9|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 07:20:18 and 07/30/2025 07:20:18 UTC. Search may be required.
2025-07-30 09:36:46.9|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 07:20:18 and 07/30/2025 07:20:18 UTC. Search may be required.
2025-07-30 09:36:46.9|<PERSON><PERSON>|<PERSON><PERSON><PERSON><PERSON>|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 07:20:18 and 07/30/2025 07:20:18 UTC. Search may be required.
2025-07-30 09:36:49.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 07:20:22 and 07/30/2025 07:20:22 UTC. Search may be required.
2025-07-30 09:36:49.1|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 09:37:43.9|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 09:52:46.8|Info|RssSyncService|Starting RSS Sync
2025-07-30 09:52:47.0|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 07:20:18 and 07/30/2025 07:20:18 UTC. Search may be required.
2025-07-30 09:52:47.0|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 07:20:18 and 07/30/2025 07:20:18 UTC. Search may be required.
2025-07-30 09:52:47.0|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 07:20:18 and 07/30/2025 07:20:18 UTC. Search may be required.
2025-07-30 09:52:47.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 07:20:18 and 07/30/2025 07:20:18 UTC. Search may be required.
2025-07-30 09:52:49.2|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 07:20:22 and 07/30/2025 07:20:22 UTC. Search may be required.
2025-07-30 09:52:49.2|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 09:53:42.7|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 09:56:17.0|Info|RefreshSeriesService|Skipping refresh of series: Better Call Saul
2025-07-30 09:56:17.0|Info|DiskScanService|Scanning Better Call Saul
2025-07-30 09:56:17.1|Info|DiskScanService|Completed scanning disk for Better Call Saul
2025-07-30 09:56:17.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 09:56:17.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 09:56:17.2|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 09:56:17.2|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-30 09:56:17.2|Info|RefreshSeriesService|Skipping refresh of series: The Big Bang Theory
2025-07-30 09:56:17.2|Info|DiskScanService|Scanning The Big Bang Theory
2025-07-30 09:56:17.4|Info|DiskScanService|Completed scanning disk for The Big Bang Theory
2025-07-30 09:56:17.4|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 09:56:17.4|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 09:56:17.4|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 09:56:17.4|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-30 09:56:17.5|Info|RefreshSeriesService|Updating Black Mirror
2025-07-30 09:56:17.7|Info|RefreshEpisodeService|Starting episode info refresh for: [253463][Black Mirror]
2025-07-30 09:56:17.9|Info|RefreshEpisodeService|Finished episode refresh for series: [253463][Black Mirror].
2025-07-30 09:56:18.1|Info|DiskScanService|Scanning Black Mirror
2025-07-30 09:56:18.3|Info|DiskScanService|Completed scanning disk for Black Mirror
2025-07-30 09:56:18.3|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 09:56:18.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 09:56:18.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 09:56:18.3|Info|ExistingExtraFileService|Found 1 possible extra files, imported 1 files.
2025-07-30 09:56:18.4|Info|RefreshSeriesService|Updating The Boys
2025-07-30 09:56:18.4|Info|RefreshEpisodeService|Starting episode info refresh for: [355567][The Boys]
2025-07-30 09:56:18.8|Info|RefreshEpisodeService|Finished episode refresh for series: [355567][The Boys].
2025-07-30 09:56:18.9|Info|DiskScanService|Scanning The Boys
2025-07-30 09:56:19.0|Info|DiskScanService|Completed scanning disk for The Boys
2025-07-30 09:56:19.0|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 09:56:19.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 09:56:19.2|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 09:56:19.2|Info|ExistingExtraFileService|Found 2 possible extra files, imported 1 files.
2025-07-30 09:56:19.2|Info|RefreshSeriesService|Skipping refresh of series: Breaking Bad
2025-07-30 09:56:19.2|Info|DiskScanService|Scanning Breaking Bad
2025-07-30 09:56:19.3|Info|DiskScanService|Completed scanning disk for Breaking Bad
2025-07-30 09:56:19.3|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 09:56:19.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 09:56:19.4|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 09:56:19.4|Info|ExistingExtraFileService|Found 1 possible extra files, imported 1 files.
2025-07-30 09:56:19.4|Info|RefreshSeriesService|Skipping refresh of series: Dark
2025-07-30 09:56:19.4|Info|DiskScanService|Scanning Dark
2025-07-30 09:56:19.5|Info|DiskScanService|Completed scanning disk for Dark
2025-07-30 09:56:19.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 09:56:19.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 09:56:19.5|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 09:56:19.5|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-30 09:56:19.6|Info|RefreshSeriesService|Skipping refresh of series: Dexter
2025-07-30 09:56:19.6|Info|DiskScanService|Scanning Dexter
2025-07-30 09:56:20.4|Info|DiskScanService|Completed scanning disk for Dexter
2025-07-30 09:56:20.6|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 09:56:20.7|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 09:56:20.8|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 09:56:20.8|Info|ExistingExtraFileService|Found 2 possible extra files, imported 0 files.
2025-07-30 09:56:20.9|Info|RefreshSeriesService|Skipping refresh of series: Dragon Ball
2025-07-30 09:56:21.0|Info|DiskScanService|Scanning Dragon Ball
2025-07-30 09:56:21.9|Info|DiskScanService|Completed scanning disk for Dragon Ball
2025-07-30 09:56:22.4|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 09:56:22.4|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 09:56:23.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 09:56:23.0|Info|ExistingExtraFileService|Found 174 possible extra files, imported 167 files.
2025-07-30 09:56:23.4|Info|RefreshSeriesService|Skipping refresh of series: Friends
2025-07-30 09:56:23.5|Info|DiskScanService|Scanning Friends
2025-07-30 09:56:23.5|Info|DiskScanService|Completed scanning disk for Friends
2025-07-30 09:56:23.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 09:56:23.6|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 09:56:23.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 09:56:23.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-30 09:56:23.6|Info|RefreshSeriesService|Skipping refresh of series: Game of Thrones
2025-07-30 09:56:23.6|Info|DiskScanService|Scanning Game of Thrones
2025-07-30 09:56:23.7|Info|DiskScanService|Completed scanning disk for Game of Thrones
2025-07-30 09:56:23.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 09:56:23.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 09:56:23.8|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 09:56:23.8|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-30 09:56:23.8|Info|RefreshSeriesService|Updating House of the Dragon
2025-07-30 09:56:23.9|Info|RefreshEpisodeService|Starting episode info refresh for: [371572][House of the Dragon]
2025-07-30 09:56:24.1|Info|RefreshEpisodeService|Finished episode refresh for series: [371572][House of the Dragon].
2025-07-30 09:56:24.1|Info|DiskScanService|Scanning House of the Dragon
2025-07-30 09:56:24.2|Info|DiskScanService|Completed scanning disk for House of the Dragon
2025-07-30 09:56:24.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 09:56:24.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 09:56:24.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 09:56:24.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-30 09:56:24.3|Info|RefreshSeriesService|Updating Outlander
2025-07-30 09:56:24.4|Info|RefreshEpisodeService|Starting episode info refresh for: [270408][Outlander]
2025-07-30 09:56:25.1|Info|RefreshEpisodeService|Finished episode refresh for series: [270408][Outlander].
2025-07-30 09:56:25.1|Info|DiskScanService|Scanning Outlander
2025-07-30 09:56:25.3|Info|DiskScanService|Completed scanning disk for Outlander
2025-07-30 09:56:25.3|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 09:56:25.4|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 09:56:25.4|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 09:56:25.4|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-30 09:56:25.4|Info|RefreshSeriesService|Skipping refresh of series: Peaky Blinders
2025-07-30 09:56:25.4|Info|DiskScanService|Scanning Peaky Blinders
2025-07-30 09:56:25.5|Info|DiskScanService|Completed scanning disk for Peaky Blinders
2025-07-30 09:56:25.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 09:56:25.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 09:56:25.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 09:56:25.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-30 09:56:25.6|Info|RefreshSeriesService|Skipping refresh of series: Prison Break
2025-07-30 09:56:25.7|Info|DiskScanService|Scanning Prison Break
2025-07-30 09:56:25.8|Info|DiskScanService|Completed scanning disk for Prison Break
2025-07-30 09:56:25.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 09:56:25.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 09:56:25.8|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 09:56:25.9|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-30 09:56:25.9|Info|RefreshSeriesService|Updating Rick and Morty
2025-07-30 09:56:25.9|Info|RefreshEpisodeService|Starting episode info refresh for: [275274][Rick and Morty]
2025-07-30 09:56:26.8|Info|RefreshEpisodeService|Finished episode refresh for series: [275274][Rick and Morty].
2025-07-30 09:56:26.8|Info|DiskScanService|Scanning Rick and Morty
2025-07-30 09:56:27.4|Info|DiskScanService|Completed scanning disk for Rick and Morty
2025-07-30 09:56:27.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 09:56:27.6|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 09:56:27.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 09:56:27.6|Info|ExistingExtraFileService|Found 1 possible extra files, imported 1 files.
2025-07-30 09:56:27.7|Info|RefreshSeriesService|Skipping refresh of series: Squid Game
2025-07-30 09:56:27.7|Info|DiskScanService|Scanning Squid Game
2025-07-30 09:56:27.7|Info|DiskScanService|Completed scanning disk for Squid Game
2025-07-30 09:56:27.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 09:56:27.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 09:56:27.8|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 09:56:27.8|Info|ExistingExtraFileService|Found 1 possible extra files, imported 1 files.
2025-07-30 09:56:27.8|Info|RefreshSeriesService|Updating Stranger Things
2025-07-30 09:56:27.9|Info|RefreshEpisodeService|Starting episode info refresh for: [305288][Stranger Things]
2025-07-30 09:56:28.1|Info|RefreshEpisodeService|Finished episode refresh for series: [305288][Stranger Things].
2025-07-30 09:56:28.2|Info|DiskScanService|Scanning Stranger Things
2025-07-30 09:56:28.2|Info|DiskScanService|Completed scanning disk for Stranger Things
2025-07-30 09:56:28.3|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 09:56:28.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 09:56:28.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 09:56:28.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-30 10:08:46.9|Info|RssSyncService|Starting RSS Sync
2025-07-30 10:08:47.5|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 07:20:18 and 07/30/2025 08:08:47 UTC. Search may be required.
2025-07-30 10:08:47.5|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 07:20:18 and 07/30/2025 08:08:47 UTC. Search may be required.
2025-07-30 10:08:47.6|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 07:20:18 and 07/30/2025 08:08:47 UTC. Search may be required.
2025-07-30 10:08:47.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 07:20:18 and 07/30/2025 08:08:47 UTC. Search may be required.
2025-07-30 10:08:47.9|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/30/2025 07:00:19 and 07/30/2025 07:41:47 UTC. Search may be required.
2025-07-30 10:08:51.6|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 07:20:22 and 07/30/2025 08:08:51 UTC. Search may be required.
2025-07-30 10:08:53.7|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 10:09:52.6|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 10:25:17.0|Info|RssSyncService|Starting RSS Sync
2025-07-30 10:25:17.1|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 08:08:47 and 07/30/2025 08:08:47 UTC. Search may be required.
2025-07-30 10:25:17.1|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 08:08:47 and 07/30/2025 08:08:47 UTC. Search may be required.
2025-07-30 10:25:17.1|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 08:08:47 and 07/30/2025 08:08:47 UTC. Search may be required.
2025-07-30 10:25:17.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 08:08:47 and 07/30/2025 08:08:47 UTC. Search may be required.
2025-07-30 10:25:19.3|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 08:08:51 and 07/30/2025 08:08:51 UTC. Search may be required.
2025-07-30 10:25:19.3|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 10:26:25.3|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 10:41:47.0|Info|RssSyncService|Starting RSS Sync
2025-07-30 10:41:47.2|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 08:08:47 and 07/30/2025 08:08:47 UTC. Search may be required.
2025-07-30 10:41:47.2|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 08:08:47 and 07/30/2025 08:08:47 UTC. Search may be required.
2025-07-30 10:41:47.2|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 08:08:47 and 07/30/2025 08:08:47 UTC. Search may be required.
2025-07-30 10:41:47.3|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 08:08:47 and 07/30/2025 08:08:47 UTC. Search may be required.
2025-07-30 10:41:49.5|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 08:08:51 and 07/30/2025 08:08:51 UTC. Search may be required.
2025-07-30 10:41:49.5|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 10:42:59.1|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 10:58:17.7|Info|RssSyncService|Starting RSS Sync
2025-07-30 10:58:23.3|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 08:08:47 and 07/30/2025 08:58:21 UTC. Search may be required.
2025-07-30 10:58:23.3|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 08:08:47 and 07/30/2025 08:58:21 UTC. Search may be required.
2025-07-30 10:58:23.3|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 08:08:47 and 07/30/2025 08:58:20 UTC. Search may be required.
2025-07-30 10:58:23.7|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 08:08:47 and 07/30/2025 08:58:21 UTC. Search may be required.
2025-07-30 10:58:24.4|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/30/2025 08:03:47 and 07/30/2025 08:47:24 UTC. Search may be required.
2025-07-30 10:58:26.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 08:08:51 and 07/30/2025 08:58:25 UTC. Search may be required.
2025-07-30 10:58:31.6|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 10:59:43.3|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 11:13:47.8|Info|SceneMappingService|Updating Scene mappings
2025-07-30 11:14:47.8|Info|RssSyncService|Starting RSS Sync
2025-07-30 11:14:50.3|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 08:58:25 and 07/30/2025 08:58:25 UTC. Search may be required.
2025-07-30 11:14:50.3|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 11:15:59.9|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 11:31:17.8|Info|RssSyncService|Starting RSS Sync
2025-07-30 11:31:20.0|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 08:58:25 and 07/30/2025 08:58:25 UTC. Search may be required.
2025-07-30 11:31:20.0|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 11:32:15.6|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 11:47:17.8|Info|RssSyncService|Starting RSS Sync
2025-07-30 11:47:18.2|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3853 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-30 11:47:18.2|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-30 11:47:18.2|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 08:58:22 and 07/30/2025 09:47:18 UTC. Search may be required.
2025-07-30 11:47:18.3|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 08:58:22 and 07/30/2025 09:47:18 UTC. Search may be required.
2025-07-30 11:47:18.9|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 08:58:22 and 07/30/2025 09:47:18 UTC. Search may be required.
2025-07-30 11:47:18.9|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 08:58:22 and 07/30/2025 09:47:18 UTC. Search may be required.
2025-07-30 11:47:19.1|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/30/2025 08:58:24 and 07/30/2025 09:25:19 UTC. Search may be required.
2025-07-30 11:47:22.9|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 08:58:25 and 07/30/2025 09:47:22 UTC. Search may be required.
2025-07-30 11:47:24.6|Info|DownloadDecisionMaker|Processing 653 releases
2025-07-30 11:48:08.7|Info|RssSyncService|RSS Sync Completed. Reports found: 653, Reports grabbed: 0
2025-07-30 12:03:17.9|Info|RssSyncService|Starting RSS Sync
2025-07-30 12:03:18.0|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 09:47:18 and 07/30/2025 09:47:18 UTC. Search may be required.
2025-07-30 12:03:18.0|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 09:47:18 and 07/30/2025 09:47:18 UTC. Search may be required.
2025-07-30 12:03:18.0|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 09:47:18 and 07/30/2025 09:47:18 UTC. Search may be required.
2025-07-30 12:03:18.0|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 09:47:18 and 07/30/2025 09:47:18 UTC. Search may be required.
2025-07-30 12:03:18.3|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3853 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-30 12:03:18.3|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-30 12:03:20.2|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 09:47:22 and 07/30/2025 09:47:22 UTC. Search may be required.
2025-07-30 12:03:20.3|Info|DownloadDecisionMaker|Processing 653 releases
2025-07-30 12:04:03.9|Info|RssSyncService|RSS Sync Completed. Reports found: 653, Reports grabbed: 0
2025-07-30 12:19:17.9|Info|RssSyncService|Starting RSS Sync
2025-07-30 12:19:18.0|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 09:47:18 and 07/30/2025 09:47:18 UTC. Search may be required.
2025-07-30 12:19:18.0|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 09:47:18 and 07/30/2025 09:47:18 UTC. Search may be required.
2025-07-30 12:19:18.0|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 09:47:18 and 07/30/2025 09:47:18 UTC. Search may be required.
2025-07-30 12:19:18.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 09:47:18 and 07/30/2025 09:47:18 UTC. Search may be required.
2025-07-30 12:19:20.1|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 07/30/2025 08:50:22 and 07/30/2025 09:37:18 UTC. Search may be required.
2025-07-30 12:19:20.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 09:47:22 and 07/30/2025 09:47:22 UTC. Search may be required.
2025-07-30 12:19:20.2|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 12:20:09.4|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 12:35:18.0|Info|RssSyncService|Starting RSS Sync
2025-07-30 12:35:18.6|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3852 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-30 12:35:18.7|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-30 12:35:18.8|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 09:47:18 and 07/30/2025 10:35:18 UTC. Search may be required.
2025-07-30 12:35:18.8|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 09:47:18 and 07/30/2025 10:35:18 UTC. Search may be required.
2025-07-30 12:35:18.9|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 09:47:18 and 07/30/2025 10:35:18 UTC. Search may be required.
2025-07-30 12:35:19.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 09:47:18 and 07/30/2025 10:35:19 UTC. Search may be required.
2025-07-30 12:35:23.6|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 09:47:22 and 07/30/2025 10:35:23 UTC. Search may be required.
2025-07-30 12:35:25.6|Info|DownloadDecisionMaker|Processing 738 releases
2025-07-30 12:36:16.6|Info|RssSyncService|RSS Sync Completed. Reports found: 738, Reports grabbed: 0
2025-07-30 12:51:18.0|Info|RssSyncService|Starting RSS Sync
2025-07-30 12:51:18.1|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 10:35:18 and 07/30/2025 10:35:18 UTC. Search may be required.
2025-07-30 12:51:18.1|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 10:35:18 and 07/30/2025 10:35:18 UTC. Search may be required.
2025-07-30 12:51:18.1|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 10:35:18 and 07/30/2025 10:35:18 UTC. Search may be required.
2025-07-30 12:51:18.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 10:35:19 and 07/30/2025 10:35:19 UTC. Search may be required.
2025-07-30 12:51:18.3|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3852 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-30 12:51:18.3|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-30 12:51:20.2|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 10:35:23 and 07/30/2025 10:35:23 UTC. Search may be required.
2025-07-30 12:51:20.3|Info|DownloadDecisionMaker|Processing 738 releases
2025-07-30 12:52:11.0|Info|RssSyncService|RSS Sync Completed. Reports found: 738, Reports grabbed: 0
2025-07-30 13:07:18.0|Info|RssSyncService|Starting RSS Sync
2025-07-30 13:07:18.1|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 10:35:18 and 07/30/2025 10:35:18 UTC. Search may be required.
2025-07-30 13:07:18.1|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 10:35:18 and 07/30/2025 10:35:18 UTC. Search may be required.
2025-07-30 13:07:18.1|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 10:35:18 and 07/30/2025 10:35:18 UTC. Search may be required.
2025-07-30 13:07:18.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 10:35:19 and 07/30/2025 10:35:19 UTC. Search may be required.
2025-07-30 13:07:20.3|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 10:35:23 and 07/30/2025 10:35:23 UTC. Search may be required.
2025-07-30 13:07:20.3|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 13:08:13.1|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 13:23:18.1|Info|RssSyncService|Starting RSS Sync
2025-07-30 13:23:18.5|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 10:35:18 and 07/30/2025 11:23:18 UTC. Search may be required.
2025-07-30 13:23:18.5|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 10:35:18 and 07/30/2025 11:23:18 UTC. Search may be required.
2025-07-30 13:23:18.6|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 10:35:18 and 07/30/2025 11:23:18 UTC. Search may be required.
2025-07-30 13:23:18.9|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 10:35:19 and 07/30/2025 11:23:18 UTC. Search may be required.
2025-07-30 13:23:18.9|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/30/2025 10:30:18 and 07/30/2025 10:48:18 UTC. Search may be required.
2025-07-30 13:23:22.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 10:35:23 and 07/30/2025 11:23:22 UTC. Search may be required.
2025-07-30 13:23:25.0|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 13:24:17.7|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 13:39:18.1|Info|RssSyncService|Starting RSS Sync
2025-07-30 13:39:18.2|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 11:23:18 and 07/30/2025 11:23:18 UTC. Search may be required.
2025-07-30 13:39:18.2|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 11:23:18 and 07/30/2025 11:23:18 UTC. Search may be required.
2025-07-30 13:39:18.2|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 11:23:18 and 07/30/2025 11:23:18 UTC. Search may be required.
2025-07-30 13:39:18.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 11:23:18 and 07/30/2025 11:23:18 UTC. Search may be required.
2025-07-30 13:39:20.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 11:23:22 and 07/30/2025 11:23:22 UTC. Search may be required.
2025-07-30 13:39:20.4|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 13:40:11.7|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 13:55:18.2|Info|RssSyncService|Starting RSS Sync
2025-07-30 13:55:18.2|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 11:23:18 and 07/30/2025 11:23:18 UTC. Search may be required.
2025-07-30 13:55:18.2|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 11:23:18 and 07/30/2025 11:23:18 UTC. Search may be required.
2025-07-30 13:55:18.3|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 11:23:18 and 07/30/2025 11:23:18 UTC. Search may be required.
2025-07-30 13:55:18.3|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 11:23:18 and 07/30/2025 11:23:18 UTC. Search may be required.
2025-07-30 13:55:18.4|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3853 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-30 13:55:18.5|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-30 13:55:20.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 11:23:22 and 07/30/2025 11:23:22 UTC. Search may be required.
2025-07-30 13:55:20.4|Info|DownloadDecisionMaker|Processing 653 releases
2025-07-30 13:56:05.4|Info|RssSyncService|RSS Sync Completed. Reports found: 653, Reports grabbed: 0
2025-07-30 14:11:18.2|Info|RssSyncService|Starting RSS Sync
2025-07-30 14:11:18.5|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3853 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-30 14:11:18.5|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-30 14:11:18.6|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 11:23:18 and 07/30/2025 12:11:18 UTC. Search may be required.
2025-07-30 14:11:18.6|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 11:23:18 and 07/30/2025 12:11:18 UTC. Search may be required.
2025-07-30 14:11:18.7|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 11:23:18 and 07/30/2025 12:11:18 UTC. Search may be required.
2025-07-30 14:11:18.8|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/30/2025 11:16:18 and 07/30/2025 11:23:18 UTC. Search may be required.
2025-07-30 14:11:18.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 11:23:18 and 07/30/2025 12:11:18 UTC. Search may be required.
2025-07-30 14:11:22.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 11:23:22 and 07/30/2025 12:11:22 UTC. Search may be required.
2025-07-30 14:11:25.4|Info|DownloadDecisionMaker|Processing 653 releases
2025-07-30 14:12:07.9|Info|RssSyncService|RSS Sync Completed. Reports found: 653, Reports grabbed: 0
2025-07-30 14:14:18.2|Info|SceneMappingService|Updating Scene mappings
2025-07-30 14:27:18.2|Info|RssSyncService|Starting RSS Sync
2025-07-30 14:27:18.3|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 12:11:18 and 07/30/2025 12:11:18 UTC. Search may be required.
2025-07-30 14:27:18.3|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 12:11:18 and 07/30/2025 12:11:18 UTC. Search may be required.
2025-07-30 14:27:18.3|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 12:11:18 and 07/30/2025 12:11:18 UTC. Search may be required.
2025-07-30 14:27:18.4|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 12:11:18 and 07/30/2025 12:11:18 UTC. Search may be required.
2025-07-30 14:27:20.4|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 07/30/2025 11:03:18 and 07/30/2025 11:27:18 UTC. Search may be required.
2025-07-30 14:27:20.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 12:11:22 and 07/30/2025 12:11:22 UTC. Search may be required.
2025-07-30 14:27:20.4|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 14:28:10.6|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 14:43:18.3|Info|RssSyncService|Starting RSS Sync
2025-07-30 14:43:18.3|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 12:11:18 and 07/30/2025 12:11:18 UTC. Search may be required.
2025-07-30 14:43:18.4|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 12:11:18 and 07/30/2025 12:11:18 UTC. Search may be required.
2025-07-30 14:43:18.4|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 12:11:18 and 07/30/2025 12:11:18 UTC. Search may be required.
2025-07-30 14:43:18.4|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 12:11:18 and 07/30/2025 12:11:18 UTC. Search may be required.
2025-07-30 14:43:20.6|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 12:11:22 and 07/30/2025 12:11:22 UTC. Search may be required.
2025-07-30 14:43:20.6|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 14:44:13.1|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 14:59:18.3|Info|RssSyncService|Starting RSS Sync
2025-07-30 14:59:19.0|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 12:11:18 and 07/30/2025 12:59:19 UTC. Search may be required.
2025-07-30 14:59:19.1|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 12:11:18 and 07/30/2025 12:59:19 UTC. Search may be required.
2025-07-30 14:59:19.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 12:11:18 and 07/30/2025 12:59:19 UTC. Search may be required.
2025-07-30 14:59:19.1|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 12:11:18 and 07/30/2025 12:59:19 UTC. Search may be required.
2025-07-30 14:59:19.4|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/30/2025 12:01:18 and 07/30/2025 12:02:19 UTC. Search may be required.
2025-07-30 14:59:23.3|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 12:11:22 and 07/30/2025 12:59:23 UTC. Search may be required.
2025-07-30 14:59:25.0|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 15:00:19.1|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 15:15:48.4|Info|RssSyncService|Starting RSS Sync
2025-07-30 15:15:48.5|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 12:59:19 and 07/30/2025 12:59:19 UTC. Search may be required.
2025-07-30 15:15:48.5|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 12:59:19 and 07/30/2025 12:59:19 UTC. Search may be required.
2025-07-30 15:15:48.5|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 12:59:19 and 07/30/2025 12:59:19 UTC. Search may be required.
2025-07-30 15:15:48.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 12:59:19 and 07/30/2025 12:59:19 UTC. Search may be required.
2025-07-30 15:15:50.6|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 12:59:23 and 07/30/2025 12:59:23 UTC. Search may be required.
2025-07-30 15:15:50.7|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 15:16:46.6|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 15:31:48.4|Info|RssSyncService|Starting RSS Sync
2025-07-30 15:31:48.5|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 12:59:19 and 07/30/2025 12:59:19 UTC. Search may be required.
2025-07-30 15:31:48.5|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 12:59:19 and 07/30/2025 12:59:19 UTC. Search may be required.
2025-07-30 15:31:48.6|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 12:59:19 and 07/30/2025 12:59:19 UTC. Search may be required.
2025-07-30 15:31:48.6|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 12:59:19 and 07/30/2025 12:59:19 UTC. Search may be required.
2025-07-30 15:31:48.7|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3852 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-30 15:31:48.7|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-30 15:31:50.6|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 12:59:23 and 07/30/2025 12:59:23 UTC. Search may be required.
2025-07-30 15:31:50.7|Info|DownloadDecisionMaker|Processing 738 releases
2025-07-30 15:32:44.4|Info|RssSyncService|RSS Sync Completed. Reports found: 738, Reports grabbed: 0
2025-07-30 15:47:48.5|Info|RssSyncService|Starting RSS Sync
2025-07-30 15:47:49.2|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3852 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-30 15:47:49.2|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-30 15:47:49.3|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 12:59:19 and 07/30/2025 13:47:49 UTC. Search may be required.
2025-07-30 15:47:49.3|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 12:59:19 and 07/30/2025 13:47:49 UTC. Search may be required.
2025-07-30 15:47:49.3|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 12:59:19 and 07/30/2025 13:47:49 UTC. Search may be required.
2025-07-30 15:47:49.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 12:59:19 and 07/30/2025 13:47:49 UTC. Search may be required.
2025-07-30 15:47:53.6|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 12:59:23 and 07/30/2025 13:47:53 UTC. Search may be required.
2025-07-30 15:47:55.7|Info|DownloadDecisionMaker|Processing 738 releases
2025-07-30 15:48:51.6|Info|RssSyncService|RSS Sync Completed. Reports found: 738, Reports grabbed: 0
2025-07-30 16:04:18.5|Info|RssSyncService|Starting RSS Sync
2025-07-30 16:04:18.6|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 13:47:49 and 07/30/2025 13:47:49 UTC. Search may be required.
2025-07-30 16:04:18.6|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 13:47:49 and 07/30/2025 13:47:49 UTC. Search may be required.
2025-07-30 16:04:18.6|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 13:47:49 and 07/30/2025 13:47:49 UTC. Search may be required.
2025-07-30 16:04:18.7|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 13:47:49 and 07/30/2025 13:47:49 UTC. Search may be required.
2025-07-30 16:04:18.8|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3853 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-30 16:04:18.9|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-30 16:04:20.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 13:47:53 and 07/30/2025 13:47:53 UTC. Search may be required.
2025-07-30 16:04:20.8|Info|DownloadDecisionMaker|Processing 653 releases
2025-07-30 16:05:07.2|Info|RssSyncService|RSS Sync Completed. Reports found: 653, Reports grabbed: 0
2025-07-30 16:20:18.6|Info|RssSyncService|Starting RSS Sync
2025-07-30 16:20:18.6|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 13:47:49 and 07/30/2025 13:47:49 UTC. Search may be required.
2025-07-30 16:20:18.6|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 13:47:49 and 07/30/2025 13:47:49 UTC. Search may be required.
2025-07-30 16:20:18.7|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 13:47:49 and 07/30/2025 13:47:49 UTC. Search may be required.
2025-07-30 16:20:18.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 13:47:49 and 07/30/2025 13:47:49 UTC. Search may be required.
2025-07-30 16:20:20.9|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 07/30/2025 13:14:48 and 07/30/2025 13:20:19 UTC. Search may be required.
2025-07-30 16:20:21.0|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 13:47:53 and 07/30/2025 13:47:53 UTC. Search may be required.
2025-07-30 16:20:21.0|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 16:21:13.4|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 16:36:18.6|Info|RssSyncService|Starting RSS Sync
2025-07-30 16:36:19.2|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 13:47:49 and 07/30/2025 14:36:19 UTC. Search may be required.
2025-07-30 16:36:19.2|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 13:47:49 and 07/30/2025 14:36:19 UTC. Search may be required.
2025-07-30 16:36:19.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 13:47:49 and 07/30/2025 14:36:19 UTC. Search may be required.
2025-07-30 16:36:19.9|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 13:47:49 and 07/30/2025 14:36:19 UTC. Search may be required.
2025-07-30 16:36:23.6|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 13:47:53 and 07/30/2025 14:36:23 UTC. Search may be required.
2025-07-30 16:36:25.7|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 16:37:28.4|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 16:53:54.1|Info|RssSyncService|Starting RSS Sync
2025-07-30 16:53:54.1|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 14:36:19 and 07/30/2025 14:36:19 UTC. Search may be required.
2025-07-30 16:53:54.2|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 14:36:19 and 07/30/2025 14:36:19 UTC. Search may be required.
2025-07-30 16:53:54.2|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 14:36:19 and 07/30/2025 14:36:19 UTC. Search may be required.
2025-07-30 16:53:54.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 14:36:19 and 07/30/2025 14:36:19 UTC. Search may be required.
2025-07-30 16:53:56.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 14:36:23 and 07/30/2025 14:36:23 UTC. Search may be required.
2025-07-30 16:53:56.4|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 16:55:02.7|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 17:10:24.1|Info|RssSyncService|Starting RSS Sync
2025-07-30 17:10:24.2|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 14:36:19 and 07/30/2025 14:36:19 UTC. Search may be required.
2025-07-30 17:10:24.2|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 14:36:19 and 07/30/2025 14:36:19 UTC. Search may be required.
2025-07-30 17:10:24.3|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 14:36:19 and 07/30/2025 14:36:19 UTC. Search may be required.
2025-07-30 17:10:24.3|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 14:36:19 and 07/30/2025 14:36:19 UTC. Search may be required.
2025-07-30 17:10:24.4|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3853 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-30 17:10:24.5|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-30 17:10:26.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 14:36:23 and 07/30/2025 14:36:23 UTC. Search may be required.
2025-07-30 17:10:26.4|Info|DownloadDecisionMaker|Processing 653 releases
2025-07-30 17:11:23.1|Info|RssSyncService|RSS Sync Completed. Reports found: 653, Reports grabbed: 0
2025-07-30 17:14:24.1|Info|SceneMappingService|Updating Scene mappings
2025-07-30 17:26:24.2|Info|RssSyncService|Starting RSS Sync
2025-07-30 17:26:25.2|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 14:36:19 and 07/30/2025 15:26:25 UTC. Search may be required.
2025-07-30 17:26:25.2|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 14:36:19 and 07/30/2025 15:26:25 UTC. Search may be required.
2025-07-30 17:26:25.3|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 14:36:19 and 07/30/2025 15:26:25 UTC. Search may be required.
2025-07-30 17:26:25.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 14:36:19 and 07/30/2025 15:26:25 UTC. Search may be required.
2025-07-30 17:26:25.6|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/30/2025 14:36:19 and 07/30/2025 14:41:25 UTC. Search may be required.
2025-07-30 17:26:26.4|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 07/30/2025 14:17:19 and 07/30/2025 14:26:25 UTC. Search may be required.
2025-07-30 17:26:29.5|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 14:36:23 and 07/30/2025 15:26:29 UTC. Search may be required.
2025-07-30 17:26:31.3|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 17:27:33.2|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 17:42:54.2|Info|RssSyncService|Starting RSS Sync
2025-07-30 17:42:54.3|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 15:26:25 and 07/30/2025 15:26:25 UTC. Search may be required.
2025-07-30 17:42:54.3|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 15:26:25 and 07/30/2025 15:26:25 UTC. Search may be required.
2025-07-30 17:42:54.3|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 15:26:25 and 07/30/2025 15:26:25 UTC. Search may be required.
2025-07-30 17:42:54.4|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 15:26:25 and 07/30/2025 15:26:25 UTC. Search may be required.
2025-07-30 17:42:56.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 15:26:29 and 07/30/2025 15:26:29 UTC. Search may be required.
2025-07-30 17:42:56.4|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 17:43:52.5|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 17:58:54.4|Info|RssSyncService|Starting RSS Sync
2025-07-30 17:58:54.5|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 15:26:25 and 07/30/2025 15:26:25 UTC. Search may be required.
2025-07-30 17:58:54.5|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 15:26:25 and 07/30/2025 15:26:25 UTC. Search may be required.
2025-07-30 17:58:54.5|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 15:26:25 and 07/30/2025 15:26:25 UTC. Search may be required.
2025-07-30 17:58:54.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 15:26:25 and 07/30/2025 15:26:25 UTC. Search may be required.
2025-07-30 17:58:56.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 15:26:29 and 07/30/2025 15:26:29 UTC. Search may be required.
2025-07-30 17:58:56.7|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 17:59:57.7|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 18:15:24.4|Info|RssSyncService|Starting RSS Sync
2025-07-30 18:15:24.8|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 15:26:25 and 07/30/2025 16:15:24 UTC. Search may be required.
2025-07-30 18:15:24.9|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 15:26:25 and 07/30/2025 16:15:24 UTC. Search may be required.
2025-07-30 18:15:24.9|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 15:26:25 and 07/30/2025 16:15:24 UTC. Search may be required.
2025-07-30 18:15:25.0|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 15:26:25 and 07/30/2025 16:15:25 UTC. Search may be required.
2025-07-30 18:15:29.3|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 15:26:29 and 07/30/2025 16:15:29 UTC. Search may be required.
2025-07-30 18:15:31.1|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 18:15:49.4|Error|DownloadDecisionMaker|Couldn't evaluate decision on Black Mirror S07 2011 1080p HDTV x264 ESP DD+ 5.1 ING DD+ 5.1 Atmos SUBS Spanish

[v4.0.15.2941] System.InvalidOperationException: No such device
 ---> Mono.Unix.UnixIOException: No such device [ENODEV].
   --- End of inner exception stack trace ---
   at Mono.Unix.UnixDriveInfo.Refresh(Boolean throwException)
   at NzbDrone.Mono.Disk.ProcMount.get_AvailableFreeSpace() in ./Sonarr.Mono/Disk/ProcMount.cs:line 23
   at NzbDrone.Mono.Disk.DiskProvider.GetAvailableSpace(String path)
   at NzbDrone.Core.DecisionEngine.Specifications.FreeSpaceSpecification.IsSatisfiedBy(RemoteEpisode subject, SearchCriteriaBase searchCriteria) in ./Sonarr.Core/DecisionEngine/Specifications/FreeSpaceSpecification.cs:line 71
   at NzbDrone.Core.DecisionEngine.DownloadDecisionMaker.EvaluateSpec(IDownloadDecisionEngineSpecification spec, RemoteEpisode remoteEpisode, SearchCriteriaBase searchCriteriaBase) in ./Sonarr.Core/DecisionEngine/DownloadDecisionMaker.cs:line 236

2025-07-30 18:16:29.3|Error|DownloadDecisionMaker|Couldn't evaluate decision on Rick y Morty  S08E10 [HDTV 1080p AC3 5.1] SPANISH

[v4.0.15.2941] System.InvalidOperationException: No such device
 ---> Mono.Unix.UnixIOException: No such device [ENODEV].
   --- End of inner exception stack trace ---
   at Mono.Unix.UnixDriveInfo.Refresh(Boolean throwException)
   at NzbDrone.Mono.Disk.ProcMount.get_AvailableFreeSpace() in ./Sonarr.Mono/Disk/ProcMount.cs:line 23
   at NzbDrone.Mono.Disk.DiskProvider.GetAvailableSpace(String path)
   at NzbDrone.Core.DecisionEngine.Specifications.FreeSpaceSpecification.IsSatisfiedBy(RemoteEpisode subject, SearchCriteriaBase searchCriteria) in ./Sonarr.Core/DecisionEngine/Specifications/FreeSpaceSpecification.cs:line 71
   at NzbDrone.Core.DecisionEngine.DownloadDecisionMaker.EvaluateSpec(IDownloadDecisionEngineSpecification spec, RemoteEpisode remoteEpisode, SearchCriteriaBase searchCriteriaBase) in ./Sonarr.Core/DecisionEngine/DownloadDecisionMaker.cs:line 236

2025-07-30 18:16:33.9|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 18:31:54.5|Info|RssSyncService|Starting RSS Sync
2025-07-30 18:31:54.6|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 16:15:24 and 07/30/2025 16:15:24 UTC. Search may be required.
2025-07-30 18:31:54.6|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 16:15:24 and 07/30/2025 16:15:24 UTC. Search may be required.
2025-07-30 18:31:54.6|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 16:15:24 and 07/30/2025 16:15:24 UTC. Search may be required.
2025-07-30 18:31:54.7|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 16:15:25 and 07/30/2025 16:15:25 UTC. Search may be required.
2025-07-30 18:31:56.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 16:15:29 and 07/30/2025 16:15:29 UTC. Search may be required.
2025-07-30 18:31:56.8|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 18:32:16.5|Error|DownloadDecisionMaker|Couldn't evaluate decision on Black Mirror S07 2011 1080p HDTV x264 ESP DD+ 5.1 ING DD+ 5.1 Atmos SUBS Spanish

[v4.0.15.2941] System.InvalidOperationException: No such device
 ---> Mono.Unix.UnixIOException: No such device [ENODEV].
   --- End of inner exception stack trace ---
   at Mono.Unix.UnixDriveInfo.Refresh(Boolean throwException)
   at NzbDrone.Mono.Disk.ProcMount.get_AvailableFreeSpace() in ./Sonarr.Mono/Disk/ProcMount.cs:line 23
   at NzbDrone.Mono.Disk.DiskProvider.GetAvailableSpace(String path)
   at NzbDrone.Core.DecisionEngine.Specifications.FreeSpaceSpecification.IsSatisfiedBy(RemoteEpisode subject, SearchCriteriaBase searchCriteria) in ./Sonarr.Core/DecisionEngine/Specifications/FreeSpaceSpecification.cs:line 71
   at NzbDrone.Core.DecisionEngine.DownloadDecisionMaker.EvaluateSpec(IDownloadDecisionEngineSpecification spec, RemoteEpisode remoteEpisode, SearchCriteriaBase searchCriteriaBase) in ./Sonarr.Core/DecisionEngine/DownloadDecisionMaker.cs:line 236

2025-07-30 18:32:58.0|Error|DownloadDecisionMaker|Couldn't evaluate decision on Rick y Morty  S08E10 [HDTV 1080p AC3 5.1] SPANISH

[v4.0.15.2941] System.InvalidOperationException: No such device
 ---> Mono.Unix.UnixIOException: No such device [ENODEV].
   --- End of inner exception stack trace ---
   at Mono.Unix.UnixDriveInfo.Refresh(Boolean throwException)
   at NzbDrone.Mono.Disk.ProcMount.get_AvailableFreeSpace() in ./Sonarr.Mono/Disk/ProcMount.cs:line 23
   at NzbDrone.Mono.Disk.DiskProvider.GetAvailableSpace(String path)
   at NzbDrone.Core.DecisionEngine.Specifications.FreeSpaceSpecification.IsSatisfiedBy(RemoteEpisode subject, SearchCriteriaBase searchCriteria) in ./Sonarr.Core/DecisionEngine/Specifications/FreeSpaceSpecification.cs:line 71
   at NzbDrone.Core.DecisionEngine.DownloadDecisionMaker.EvaluateSpec(IDownloadDecisionEngineSpecification spec, RemoteEpisode remoteEpisode, SearchCriteriaBase searchCriteriaBase) in ./Sonarr.Core/DecisionEngine/DownloadDecisionMaker.cs:line 236

2025-07-30 18:33:02.9|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 18:48:13.2|Info|Bootstrap|Starting Sonarr - /app/sonarr/bin/Sonarr - Version 4.0.15.2941
2025-07-30 18:48:14.0|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-07-30 18:48:14.3|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-07-30 18:48:18.3|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-07-30 18:48:19.8|Info|MigrationController|*** Migrating data source=/config/sonarr.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-07-30 18:48:20.3|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-07-30 18:48:20.4|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-07-30 18:48:20.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-07-30 18:48:20.9|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-07-30 18:48:20.9|Info|FluentMigrator.Runner.MigrationRunner|=> 0.4637402s
2025-07-30 18:48:20.9|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-07-30 18:48:21.0|Info|FluentMigrator.Runner.MigrationRunner|=> 0.4922517s
2025-07-30 18:48:21.3|Info|MigrationController|*** Migrating data source=/config/logs.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-07-30 18:48:21.4|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-07-30 18:48:21.4|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-07-30 18:48:21.4|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-07-30 18:48:21.4|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-07-30 18:48:21.5|Info|FluentMigrator.Runner.MigrationRunner|=> 0.0521496s
2025-07-30 18:48:21.5|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-07-30 18:48:21.5|Info|FluentMigrator.Runner.MigrationRunner|=> 0.065773s
2025-07-30 18:48:23.4|Info|Microsoft.Hosting.Lifetime|Now listening on: http://[::]:8989
2025-07-30 18:48:26.2|Info|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.
2025-07-30 18:48:26.4|Info|Microsoft.Hosting.Lifetime|Hosting environment: Production
2025-07-30 18:48:26.4|Info|Microsoft.Hosting.Lifetime|Content root path: /app/sonarr/bin
2025-07-30 18:48:26.8|Info|ManagedHttpDispatcher|IPv4 is available: True, IPv6 will be disabled
2025-07-30 18:48:55.9|Info|RssSyncService|Starting RSS Sync
2025-07-30 18:48:57.5|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 16:15:24 and 07/30/2025 16:48:57 UTC. Search may be required.
2025-07-30 18:48:57.9|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 16:15:24 and 07/30/2025 16:48:57 UTC. Search may be required.
2025-07-30 18:48:57.9|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 16:15:24 and 07/30/2025 16:48:57 UTC. Search may be required.
2025-07-30 18:48:58.3|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 16:15:25 and 07/30/2025 16:48:58 UTC. Search may be required.
2025-07-30 18:49:03.3|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 16:15:29 and 07/30/2025 16:49:03 UTC. Search may be required.
2025-07-30 18:49:04.2|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 18:50:09.4|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 19:05:25.9|Info|RssSyncService|Starting RSS Sync
2025-07-30 19:05:26.7|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 16:48:58 and 07/30/2025 16:48:58 UTC. Search may be required.
2025-07-30 19:05:26.7|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 16:48:57 and 07/30/2025 16:48:57 UTC. Search may be required.
2025-07-30 19:05:26.8|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 16:48:57 and 07/30/2025 16:48:57 UTC. Search may be required.
2025-07-30 19:05:26.9|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 16:48:57 and 07/30/2025 16:48:57 UTC. Search may be required.
2025-07-30 19:05:28.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 16:49:03 and 07/30/2025 16:49:03 UTC. Search may be required.
2025-07-30 19:05:28.1|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 19:06:44.1|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 19:21:56.1|Info|RssSyncService|Starting RSS Sync
2025-07-30 19:21:58.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 16:48:58 and 07/30/2025 16:48:58 UTC. Search may be required.
2025-07-30 19:21:58.2|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 16:48:57 and 07/30/2025 16:48:57 UTC. Search may be required.
2025-07-30 19:21:58.2|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 16:48:57 and 07/30/2025 16:48:57 UTC. Search may be required.
2025-07-30 19:21:58.8|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 16:48:57 and 07/30/2025 16:48:57 UTC. Search may be required.
2025-07-30 19:21:59.0|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 16:49:03 and 07/30/2025 16:49:03 UTC. Search may be required.
2025-07-30 19:21:59.1|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 19:23:49.3|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 19:38:56.0|Info|RssSyncService|Starting RSS Sync
2025-07-30 19:38:57.3|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 16:48:57 and 07/30/2025 17:38:57 UTC. Search may be required.
2025-07-30 19:38:57.3|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 16:48:57 and 07/30/2025 17:38:57 UTC. Search may be required.
2025-07-30 19:38:57.6|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 16:48:58 and 07/30/2025 17:38:57 UTC. Search may be required.
2025-07-30 19:38:57.7|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 16:48:57 and 07/30/2025 17:38:57 UTC. Search may be required.
2025-07-30 19:38:58.5|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/30/2025 16:38:59 and 07/30/2025 17:29:58 UTC. Search may be required.
2025-07-30 19:39:01.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 16:49:03 and 07/30/2025 17:39:01 UTC. Search may be required.
2025-07-30 19:39:03.8|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 19:40:10.3|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 19:55:26.1|Info|RssSyncService|Starting RSS Sync
2025-07-30 19:55:27.3|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 17:38:57 and 07/30/2025 17:38:57 UTC. Search may be required.
2025-07-30 19:55:27.3|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 17:38:57 and 07/30/2025 17:38:57 UTC. Search may be required.
2025-07-30 19:55:27.4|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 17:38:57 and 07/30/2025 17:38:57 UTC. Search may be required.
2025-07-30 19:55:27.5|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 17:38:57 and 07/30/2025 17:38:57 UTC. Search may be required.
2025-07-30 19:55:28.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 17:39:01 and 07/30/2025 17:39:01 UTC. Search may be required.
2025-07-30 19:55:28.4|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 19:56:32.9|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 20:11:56.2|Info|RssSyncService|Starting RSS Sync
2025-07-30 20:11:56.5|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 17:38:57 and 07/30/2025 17:38:57 UTC. Search may be required.
2025-07-30 20:11:56.5|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 17:38:57 and 07/30/2025 17:38:57 UTC. Search may be required.
2025-07-30 20:11:56.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 17:38:57 and 07/30/2025 17:38:57 UTC. Search may be required.
2025-07-30 20:11:56.8|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 17:38:57 and 07/30/2025 17:38:57 UTC. Search may be required.
2025-07-30 20:11:58.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 17:39:01 and 07/30/2025 17:39:01 UTC. Search may be required.
2025-07-30 20:11:58.4|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 20:13:01.4|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 20:14:26.3|Info|SceneMappingService|Updating Scene mappings
2025-07-30 20:28:26.3|Info|RssSyncService|Starting RSS Sync
2025-07-30 20:28:29.1|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 17:38:57 and 07/30/2025 18:28:28 UTC. Search may be required.
2025-07-30 20:28:29.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 17:38:57 and 07/30/2025 18:28:28 UTC. Search may be required.
2025-07-30 20:28:29.1|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 17:38:57 and 07/30/2025 18:28:28 UTC. Search may be required.
2025-07-30 20:28:29.4|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 17:38:57 and 07/30/2025 18:28:28 UTC. Search may be required.
2025-07-30 20:28:31.1|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/30/2025 17:38:58 and 07/30/2025 17:44:30 UTC. Search may be required.
2025-07-30 20:28:32.2|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 17:39:01 and 07/30/2025 18:28:32 UTC. Search may be required.
2025-07-30 20:28:37.2|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 20:30:17.9|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 20:45:26.4|Info|RssSyncService|Starting RSS Sync
2025-07-30 20:45:26.6|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 18:28:28 and 07/30/2025 18:28:28 UTC. Search may be required.
2025-07-30 20:45:26.6|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 18:28:28 and 07/30/2025 18:28:28 UTC. Search may be required.
2025-07-30 20:45:28.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 18:28:32 and 07/30/2025 18:28:32 UTC. Search may be required.
2025-07-30 20:45:28.8|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 20:46:59.0|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 21:02:26.7|Info|RssSyncService|Starting RSS Sync
2025-07-30 21:02:27.1|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 18:28:28 and 07/30/2025 18:28:28 UTC. Search may be required.
2025-07-30 21:02:27.2|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 18:28:28 and 07/30/2025 18:28:28 UTC. Search may be required.
2025-07-30 21:02:29.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 18:28:32 and 07/30/2025 18:28:32 UTC. Search may be required.
2025-07-30 21:02:29.2|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 21:04:22.0|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 21:19:26.7|Info|RssSyncService|Starting RSS Sync
2025-07-30 21:19:27.2|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 18:28:28 and 07/30/2025 19:19:27 UTC. Search may be required.
2025-07-30 21:19:27.3|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 18:28:28 and 07/30/2025 19:19:27 UTC. Search may be required.
2025-07-30 21:19:27.4|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 18:28:29 and 07/30/2025 19:19:27 UTC. Search may be required.
2025-07-30 21:19:27.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 18:28:29 and 07/30/2025 19:19:27 UTC. Search may be required.
2025-07-30 21:19:27.8|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/30/2025 18:20:29 and 07/30/2025 19:07:27 UTC. Search may be required.
2025-07-30 21:19:28.9|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 07/30/2025 18:21:29 and 07/30/2025 18:33:27 UTC. Search may be required.
2025-07-30 21:19:31.5|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 18:28:32 and 07/30/2025 19:19:31 UTC. Search may be required.
2025-07-30 21:19:33.8|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 21:20:33.0|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 21:35:56.8|Info|RssSyncService|Starting RSS Sync
2025-07-30 21:35:56.9|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 19:19:27 and 07/30/2025 19:19:27 UTC. Search may be required.
2025-07-30 21:35:56.9|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 19:19:27 and 07/30/2025 19:19:27 UTC. Search may be required.
2025-07-30 21:35:56.9|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 19:19:27 and 07/30/2025 19:19:27 UTC. Search may be required.
2025-07-30 21:35:56.9|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 19:19:27 and 07/30/2025 19:19:27 UTC. Search may be required.
2025-07-30 21:35:59.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 19:19:31 and 07/30/2025 19:19:31 UTC. Search may be required.
2025-07-30 21:35:59.1|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 21:36:52.7|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 21:40:26.8|Info|RecycleBinProvider|Recycle Bin has not been configured, cannot cleanup.
2025-07-30 21:41:26.8|Info|HousekeepingService|Running housecleaning tasks
2025-07-30 21:41:27.5|Info|Database|Vacuuming Log database
2025-07-30 21:41:28.6|Info|Database|Log database compressed
2025-07-30 21:41:28.6|Info|Database|Vacuuming Main database
2025-07-30 21:41:31.3|Info|Database|Main database compressed
2025-07-30 21:51:56.9|Info|RssSyncService|Starting RSS Sync
2025-07-30 21:51:57.0|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 19:19:27 and 07/30/2025 19:19:27 UTC. Search may be required.
2025-07-30 21:51:57.0|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 19:19:27 and 07/30/2025 19:19:27 UTC. Search may be required.
2025-07-30 21:51:57.0|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 19:19:27 and 07/30/2025 19:19:27 UTC. Search may be required.
2025-07-30 21:51:57.0|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 19:19:27 and 07/30/2025 19:19:27 UTC. Search may be required.
2025-07-30 21:51:59.2|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 19:19:31 and 07/30/2025 19:19:31 UTC. Search may be required.
2025-07-30 21:51:59.2|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 21:52:54.3|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 21:56:57.0|Info|RefreshSeriesService|Skipping refresh of series: Better Call Saul
2025-07-30 21:56:57.0|Info|DiskScanService|Scanning Better Call Saul
2025-07-30 21:56:57.2|Info|DiskScanService|Completed scanning disk for Better Call Saul
2025-07-30 21:56:57.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 21:56:57.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 21:56:57.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 21:56:57.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-30 21:56:57.3|Info|RefreshSeriesService|Skipping refresh of series: The Big Bang Theory
2025-07-30 21:56:57.3|Info|DiskScanService|Scanning The Big Bang Theory
2025-07-30 21:56:57.5|Info|DiskScanService|Completed scanning disk for The Big Bang Theory
2025-07-30 21:56:57.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 21:56:57.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 21:56:57.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 21:56:57.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-30 21:56:57.6|Info|RefreshSeriesService|Updating Black Mirror
2025-07-30 21:56:58.1|Info|RefreshEpisodeService|Starting episode info refresh for: [253463][Black Mirror]
2025-07-30 21:56:58.3|Info|RefreshEpisodeService|Finished episode refresh for series: [253463][Black Mirror].
2025-07-30 21:56:58.4|Info|DiskScanService|Scanning Black Mirror
2025-07-30 21:56:58.8|Info|DiskScanService|Completed scanning disk for Black Mirror
2025-07-30 21:56:58.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 21:56:58.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 21:56:58.9|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 21:56:58.9|Info|ExistingExtraFileService|Found 1 possible extra files, imported 1 files.
2025-07-30 21:56:58.9|Info|RefreshSeriesService|Updating The Boys
2025-07-30 21:56:59.0|Info|RefreshEpisodeService|Starting episode info refresh for: [355567][The Boys]
2025-07-30 21:56:59.5|Info|RefreshEpisodeService|Finished episode refresh for series: [355567][The Boys].
2025-07-30 21:56:59.6|Info|DiskScanService|Scanning The Boys
2025-07-30 21:56:59.7|Info|DiskScanService|Completed scanning disk for The Boys
2025-07-30 21:56:59.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 21:56:59.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 21:56:59.9|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 21:56:59.9|Info|ExistingExtraFileService|Found 2 possible extra files, imported 1 files.
2025-07-30 21:57:00.0|Info|RefreshSeriesService|Skipping refresh of series: Breaking Bad
2025-07-30 21:57:00.0|Info|DiskScanService|Scanning Breaking Bad
2025-07-30 21:57:00.2|Info|DiskScanService|Completed scanning disk for Breaking Bad
2025-07-30 21:57:00.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 21:57:00.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 21:57:00.2|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 21:57:00.3|Info|ExistingExtraFileService|Found 1 possible extra files, imported 1 files.
2025-07-30 21:57:00.3|Info|RefreshSeriesService|Skipping refresh of series: Dark
2025-07-30 21:57:00.3|Info|DiskScanService|Scanning Dark
2025-07-30 21:57:00.4|Info|DiskScanService|Completed scanning disk for Dark
2025-07-30 21:57:00.4|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 21:57:00.4|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 21:57:00.5|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 21:57:00.5|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-30 21:57:00.5|Info|RefreshSeriesService|Skipping refresh of series: Dexter
2025-07-30 21:57:00.5|Info|DiskScanService|Scanning Dexter
2025-07-30 21:57:01.3|Info|DiskScanService|Completed scanning disk for Dexter
2025-07-30 21:57:01.4|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 21:57:01.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 21:57:01.7|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 21:57:01.7|Info|ExistingExtraFileService|Found 2 possible extra files, imported 0 files.
2025-07-30 21:57:01.7|Info|RefreshSeriesService|Skipping refresh of series: Dragon Ball
2025-07-30 21:57:01.8|Info|DiskScanService|Scanning Dragon Ball
2025-07-30 21:57:03.2|Info|DiskScanService|Completed scanning disk for Dragon Ball
2025-07-30 21:57:03.6|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 21:57:03.6|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 21:57:04.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 21:57:04.3|Info|ExistingExtraFileService|Found 174 possible extra files, imported 167 files.
2025-07-30 21:57:04.7|Info|RefreshSeriesService|Skipping refresh of series: Friends
2025-07-30 21:57:04.7|Info|DiskScanService|Scanning Friends
2025-07-30 21:57:04.8|Info|DiskScanService|Completed scanning disk for Friends
2025-07-30 21:57:04.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 21:57:04.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 21:57:04.9|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 21:57:04.9|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-30 21:57:04.9|Info|RefreshSeriesService|Skipping refresh of series: Game of Thrones
2025-07-30 21:57:04.9|Info|DiskScanService|Scanning Game of Thrones
2025-07-30 21:57:05.1|Info|DiskScanService|Completed scanning disk for Game of Thrones
2025-07-30 21:57:05.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 21:57:05.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 21:57:05.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 21:57:05.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-30 21:57:05.2|Info|RefreshSeriesService|Updating House of the Dragon
2025-07-30 21:57:05.2|Info|RefreshEpisodeService|Starting episode info refresh for: [371572][House of the Dragon]
2025-07-30 21:57:05.4|Info|RefreshEpisodeService|Finished episode refresh for series: [371572][House of the Dragon].
2025-07-30 21:57:05.4|Info|DiskScanService|Scanning House of the Dragon
2025-07-30 21:57:05.5|Info|DiskScanService|Completed scanning disk for House of the Dragon
2025-07-30 21:57:05.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 21:57:05.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 21:57:05.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 21:57:05.9|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-30 21:57:05.9|Info|RefreshSeriesService|Updating Outlander
2025-07-30 21:57:05.9|Info|RefreshEpisodeService|Starting episode info refresh for: [270408][Outlander]
2025-07-30 21:57:06.6|Info|RefreshEpisodeService|Finished episode refresh for series: [270408][Outlander].
2025-07-30 21:57:06.6|Info|DiskScanService|Scanning Outlander
2025-07-30 21:57:06.7|Info|DiskScanService|Completed scanning disk for Outlander
2025-07-30 21:57:06.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 21:57:06.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 21:57:06.8|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 21:57:06.8|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-30 21:57:06.8|Info|RefreshSeriesService|Skipping refresh of series: Peaky Blinders
2025-07-30 21:57:06.9|Info|DiskScanService|Scanning Peaky Blinders
2025-07-30 21:57:06.9|Info|DiskScanService|Completed scanning disk for Peaky Blinders
2025-07-30 21:57:06.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 21:57:06.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 21:57:07.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 21:57:07.0|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-30 21:57:07.0|Info|RefreshSeriesService|Skipping refresh of series: Prison Break
2025-07-30 21:57:07.0|Info|DiskScanService|Scanning Prison Break
2025-07-30 21:57:07.2|Info|DiskScanService|Completed scanning disk for Prison Break
2025-07-30 21:57:07.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 21:57:07.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 21:57:07.2|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 21:57:07.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-30 21:57:07.3|Info|RefreshSeriesService|Updating Rick and Morty
2025-07-30 21:57:07.3|Info|RefreshEpisodeService|Starting episode info refresh for: [275274][Rick and Morty]
2025-07-30 21:57:08.1|Info|RefreshEpisodeService|Finished episode refresh for series: [275274][Rick and Morty].
2025-07-30 21:57:08.2|Info|DiskScanService|Scanning Rick and Morty
2025-07-30 21:57:08.8|Info|DiskScanService|Completed scanning disk for Rick and Morty
2025-07-30 21:57:08.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 21:57:08.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 21:57:08.9|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 21:57:08.9|Info|ExistingExtraFileService|Found 1 possible extra files, imported 1 files.
2025-07-30 21:57:09.0|Info|RefreshSeriesService|Skipping refresh of series: Squid Game
2025-07-30 21:57:09.0|Info|DiskScanService|Scanning Squid Game
2025-07-30 21:57:09.0|Info|DiskScanService|Completed scanning disk for Squid Game
2025-07-30 21:57:09.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 21:57:09.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 21:57:09.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 21:57:09.1|Info|ExistingExtraFileService|Found 1 possible extra files, imported 1 files.
2025-07-30 21:57:09.1|Info|RefreshSeriesService|Updating Stranger Things
2025-07-30 21:57:09.2|Info|RefreshEpisodeService|Starting episode info refresh for: [305288][Stranger Things]
2025-07-30 21:57:09.4|Info|RefreshEpisodeService|Finished episode refresh for series: [305288][Stranger Things].
2025-07-30 21:57:09.5|Info|DiskScanService|Scanning Stranger Things
2025-07-30 21:57:09.5|Info|DiskScanService|Completed scanning disk for Stranger Things
2025-07-30 21:57:09.6|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-30 21:57:09.6|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-30 21:57:09.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-30 21:57:09.6|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-30 22:07:57.0|Info|RssSyncService|Starting RSS Sync
2025-07-30 22:07:57.2|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3977 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Network unreachable&#xA; ---&gt; System.Net.Http.HttpRequestException: Network unreachable (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (101): Network unreachable&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-30 22:07:57.3|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 19:19:27 and 07/30/2025 20:07:57 UTC. Search may be required.
2025-07-30 22:07:57.3|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-30 22:07:57.4|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 19:19:27 and 07/30/2025 20:07:57 UTC. Search may be required.
2025-07-30 22:07:57.4|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 19:19:27 and 07/30/2025 20:07:57 UTC. Search may be required.
2025-07-30 22:07:57.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 19:19:27 and 07/30/2025 20:07:57 UTC. Search may be required.
2025-07-30 22:07:57.6|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/30/2025 19:14:27 and 07/30/2025 19:15:57 UTC. Search may be required.
2025-07-30 22:08:01.2|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 19:19:31 and 07/30/2025 20:08:01 UTC. Search may be required.
2025-07-30 22:08:04.2|Info|DownloadDecisionMaker|Processing 653 releases
2025-07-30 22:08:51.8|Info|RssSyncService|RSS Sync Completed. Reports found: 653, Reports grabbed: 0
2025-07-30 22:23:57.1|Info|RssSyncService|Starting RSS Sync
2025-07-30 22:23:57.1|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 20:07:57 and 07/30/2025 20:07:57 UTC. Search may be required.
2025-07-30 22:23:57.2|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 20:07:57 and 07/30/2025 20:07:57 UTC. Search may be required.
2025-07-30 22:23:57.2|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 20:07:57 and 07/30/2025 20:07:57 UTC. Search may be required.
2025-07-30 22:23:57.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 20:07:57 and 07/30/2025 20:07:57 UTC. Search may be required.
2025-07-30 22:23:57.4|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-30 22:23:57.5|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-30 22:23:59.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 20:08:01 and 07/30/2025 20:08:01 UTC. Search may be required.
2025-07-30 22:23:59.4|Info|DownloadDecisionMaker|Processing 653 releases
2025-07-30 22:24:45.7|Info|RssSyncService|RSS Sync Completed. Reports found: 653, Reports grabbed: 0
2025-07-30 22:39:57.1|Info|RssSyncService|Starting RSS Sync
2025-07-30 22:39:57.2|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 20:07:57 and 07/30/2025 20:07:57 UTC. Search may be required.
2025-07-30 22:39:57.2|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 20:07:57 and 07/30/2025 20:07:57 UTC. Search may be required.
2025-07-30 22:39:57.2|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 20:07:57 and 07/30/2025 20:07:57 UTC. Search may be required.
2025-07-30 22:39:57.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 20:07:57 and 07/30/2025 20:07:57 UTC. Search may be required.
2025-07-30 22:39:59.3|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 07/30/2025 19:18:27 and 07/30/2025 19:39:57 UTC. Search may be required.
2025-07-30 22:39:59.3|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 20:08:01 and 07/30/2025 20:08:01 UTC. Search may be required.
2025-07-30 22:39:59.4|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 22:40:52.3|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 22:55:57.2|Info|RssSyncService|Starting RSS Sync
2025-07-30 22:55:57.6|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 20:07:57 and 07/30/2025 20:55:57 UTC. Search may be required.
2025-07-30 22:55:57.6|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 20:07:57 and 07/30/2025 20:55:57 UTC. Search may be required.
2025-07-30 22:55:57.6|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 20:07:57 and 07/30/2025 20:55:57 UTC. Search may be required.
2025-07-30 22:55:57.8|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/30/2025 20:04:57 and 07/30/2025 20:38:57 UTC. Search may be required.
2025-07-30 22:55:57.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 20:07:57 and 07/30/2025 20:55:57 UTC. Search may be required.
2025-07-30 22:56:01.6|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 20:08:01 and 07/30/2025 20:56:01 UTC. Search may be required.
2025-07-30 22:56:04.0|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 22:56:53.5|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 23:11:57.2|Info|RssSyncService|Starting RSS Sync
2025-07-30 23:11:57.3|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 20:55:57 and 07/30/2025 20:55:57 UTC. Search may be required.
2025-07-30 23:11:57.3|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 20:55:57 and 07/30/2025 20:55:57 UTC. Search may be required.
2025-07-30 23:11:57.3|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 20:55:57 and 07/30/2025 20:55:57 UTC. Search may be required.
2025-07-30 23:11:57.4|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 20:55:57 and 07/30/2025 20:55:57 UTC. Search may be required.
2025-07-30 23:11:59.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 20:56:01 and 07/30/2025 20:56:01 UTC. Search may be required.
2025-07-30 23:11:59.4|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 23:12:52.9|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 23:14:57.2|Info|SceneMappingService|Updating Scene mappings
2025-07-30 23:27:57.2|Info|RssSyncService|Starting RSS Sync
2025-07-30 23:27:57.3|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 20:55:57 and 07/30/2025 20:55:57 UTC. Search may be required.
2025-07-30 23:27:57.3|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 20:55:57 and 07/30/2025 20:55:57 UTC. Search may be required.
2025-07-30 23:27:57.3|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 20:55:57 and 07/30/2025 20:55:57 UTC. Search may be required.
2025-07-30 23:27:57.3|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 20:55:57 and 07/30/2025 20:55:57 UTC. Search may be required.
2025-07-30 23:27:59.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 20:56:01 and 07/30/2025 20:56:01 UTC. Search may be required.
2025-07-30 23:27:59.5|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 23:28:51.7|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 23:43:57.3|Info|RssSyncService|Starting RSS Sync
2025-07-30 23:43:57.6|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 20:55:57 and 07/30/2025 21:43:57 UTC. Search may be required.
2025-07-30 23:43:57.7|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 20:55:57 and 07/30/2025 21:43:57 UTC. Search may be required.
2025-07-30 23:43:57.7|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 20:55:57 and 07/30/2025 21:43:57 UTC. Search may be required.
2025-07-30 23:43:57.9|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 20:55:57 and 07/30/2025 21:43:57 UTC. Search may be required.
2025-07-30 23:44:01.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 20:56:01 and 07/30/2025 21:44:01 UTC. Search may be required.
2025-07-30 23:44:04.2|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-30 23:44:56.7|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-30 23:59:57.3|Info|RssSyncService|Starting RSS Sync
2025-07-30 23:59:57.4|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 21:43:57 and 07/30/2025 21:43:57 UTC. Search may be required.
2025-07-30 23:59:57.4|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 21:43:57 and 07/30/2025 21:43:57 UTC. Search may be required.
2025-07-30 23:59:57.4|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 21:43:57 and 07/30/2025 21:43:57 UTC. Search may be required.
2025-07-30 23:59:57.4|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 21:43:57 and 07/30/2025 21:43:57 UTC. Search may be required.
2025-07-30 23:59:59.5|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 21:44:01 and 07/30/2025 21:44:01 UTC. Search may be required.
2025-07-30 23:59:59.6|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-31 00:00:50.9|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-31 00:15:57.4|Info|RssSyncService|Starting RSS Sync
2025-07-31 00:15:57.5|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 21:43:57 and 07/30/2025 21:43:57 UTC. Search may be required.
2025-07-31 00:15:57.5|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 21:43:57 and 07/30/2025 21:43:57 UTC. Search may be required.
2025-07-31 00:15:57.5|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 21:43:57 and 07/30/2025 21:43:57 UTC. Search may be required.
2025-07-31 00:15:57.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 21:43:57 and 07/30/2025 21:43:57 UTC. Search may be required.
2025-07-31 00:15:57.6|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 00:15:57.7|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 00:15:59.6|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 21:44:01 and 07/30/2025 21:44:01 UTC. Search may be required.
2025-07-31 00:15:59.7|Info|DownloadDecisionMaker|Processing 653 releases
2025-07-31 00:16:43.8|Info|RssSyncService|RSS Sync Completed. Reports found: 653, Reports grabbed: 0
2025-07-31 00:31:57.4|Info|RssSyncService|Starting RSS Sync
2025-07-31 00:31:57.7|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 00:31:57.7|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 00:31:57.9|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 21:43:57 and 07/30/2025 22:31:57 UTC. Search may be required.
2025-07-31 00:31:57.9|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 21:43:57 and 07/30/2025 22:31:57 UTC. Search may be required.
2025-07-31 00:31:58.0|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 21:43:57 and 07/30/2025 22:31:57 UTC. Search may be required.
2025-07-31 00:31:58.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 21:43:57 and 07/30/2025 22:31:58 UTC. Search may be required.
2025-07-31 00:32:02.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 21:44:01 and 07/30/2025 22:32:02 UTC. Search may be required.
2025-07-31 00:32:04.3|Info|DownloadDecisionMaker|Processing 653 releases
2025-07-31 00:32:50.3|Info|RssSyncService|RSS Sync Completed. Reports found: 653, Reports grabbed: 0
2025-07-31 00:47:57.5|Info|RssSyncService|Starting RSS Sync
2025-07-31 00:47:57.6|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 22:31:57 and 07/30/2025 22:31:57 UTC. Search may be required.
2025-07-31 00:47:57.6|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 22:31:57 and 07/30/2025 22:31:57 UTC. Search may be required.
2025-07-31 00:47:57.6|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 22:31:58 and 07/30/2025 22:31:58 UTC. Search may be required.
2025-07-31 00:47:57.7|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 22:31:57 and 07/30/2025 22:31:57 UTC. Search may be required.
2025-07-31 00:47:59.7|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 07/30/2025 21:27:57 and 07/30/2025 21:47:58 UTC. Search may be required.
2025-07-31 00:47:59.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 22:32:02 and 07/30/2025 22:32:02 UTC. Search may be required.
2025-07-31 00:47:59.8|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-31 00:48:56.9|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-31 01:03:57.5|Info|RssSyncService|Starting RSS Sync
2025-07-31 01:03:57.6|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 22:31:57 and 07/30/2025 22:31:57 UTC. Search may be required.
2025-07-31 01:03:57.6|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 22:31:57 and 07/30/2025 22:31:57 UTC. Search may be required.
2025-07-31 01:03:57.7|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 22:31:58 and 07/30/2025 22:31:58 UTC. Search may be required.
2025-07-31 01:03:57.7|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 22:31:57 and 07/30/2025 22:31:57 UTC. Search may be required.
2025-07-31 01:03:59.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 22:32:02 and 07/30/2025 22:32:02 UTC. Search may be required.
2025-07-31 01:03:59.8|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-31 01:04:49.9|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-31 01:19:57.6|Info|RssSyncService|Starting RSS Sync
2025-07-31 01:19:58.0|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 22:31:57 and 07/30/2025 23:19:57 UTC. Search may be required.
2025-07-31 01:19:58.0|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 22:31:57 and 07/30/2025 23:19:57 UTC. Search may be required.
2025-07-31 01:19:58.0|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 22:31:57 and 07/30/2025 23:19:57 UTC. Search may be required.
2025-07-31 01:19:58.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 22:31:58 and 07/30/2025 23:19:58 UTC. Search may be required.
2025-07-31 01:20:02.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 22:32:02 and 07/30/2025 23:20:02 UTC. Search may be required.
2025-07-31 01:20:04.1|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-31 01:20:52.2|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-31 01:35:57.6|Info|RssSyncService|Starting RSS Sync
2025-07-31 01:35:57.7|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 23:19:57 and 07/30/2025 23:19:57 UTC. Search may be required.
2025-07-31 01:35:57.7|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 23:19:57 and 07/30/2025 23:19:57 UTC. Search may be required.
2025-07-31 01:35:57.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 23:19:58 and 07/30/2025 23:19:58 UTC. Search may be required.
2025-07-31 01:35:59.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 23:20:02 and 07/30/2025 23:20:02 UTC. Search may be required.
2025-07-31 01:35:59.9|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-31 01:36:48.8|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-31 01:51:57.6|Info|RssSyncService|Starting RSS Sync
2025-07-31 01:51:57.8|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 23:19:57 and 07/30/2025 23:19:57 UTC. Search may be required.
2025-07-31 01:51:57.8|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 23:19:57 and 07/30/2025 23:19:57 UTC. Search may be required.
2025-07-31 01:51:57.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 23:19:58 and 07/30/2025 23:19:58 UTC. Search may be required.
2025-07-31 01:52:00.0|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 23:20:02 and 07/30/2025 23:20:02 UTC. Search may be required.
2025-07-31 01:52:00.1|Info|DownloadDecisionMaker|Processing 753 releases
2025-07-31 01:52:50.1|Info|RssSyncService|RSS Sync Completed. Reports found: 753, Reports grabbed: 0
2025-07-31 02:07:57.7|Info|RssSyncService|Starting RSS Sync
2025-07-31 02:07:58.1|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/30/2025 23:19:57 and 07/31/2025 00:07:58 UTC. Search may be required.
2025-07-31 02:07:58.2|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/30/2025 23:19:57 and 07/31/2025 00:07:58 UTC. Search may be required.
2025-07-31 02:07:58.2|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/30/2025 23:19:58 and 07/31/2025 00:07:58 UTC. Search may be required.
2025-07-31 02:07:58.3|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/30/2025 22:32:58 and 07/30/2025 23:35:58 UTC. Search may be required.
2025-07-31 02:07:58.4|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/30/2025 23:19:58 and 07/31/2025 00:07:58 UTC. Search may be required.
2025-07-31 02:08:02.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/30/2025 23:20:02 and 07/31/2025 00:08:02 UTC. Search may be required.
2025-07-31 02:08:05.0|Info|DownloadDecisionMaker|Processing 744 releases
2025-07-31 02:08:55.5|Info|RssSyncService|RSS Sync Completed. Reports found: 744, Reports grabbed: 0
2025-07-31 02:15:27.7|Info|SceneMappingService|Updating Scene mappings
2025-07-31 02:23:57.7|Info|RssSyncService|Starting RSS Sync
2025-07-31 02:23:57.8|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 00:07:58 and 07/31/2025 00:07:58 UTC. Search may be required.
2025-07-31 02:23:57.8|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 00:07:58 and 07/31/2025 00:07:58 UTC. Search may be required.
2025-07-31 02:23:57.8|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/31/2025 00:07:58 and 07/31/2025 00:07:58 UTC. Search may be required.
2025-07-31 02:23:57.9|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 00:07:58 and 07/31/2025 00:07:58 UTC. Search may be required.
2025-07-31 02:23:58.0|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 02:23:58.0|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 02:24:00.0|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 00:08:02 and 07/31/2025 00:08:02 UTC. Search may be required.
2025-07-31 02:24:00.0|Info|DownloadDecisionMaker|Processing 644 releases
2025-07-31 02:24:44.8|Info|RssSyncService|RSS Sync Completed. Reports found: 644, Reports grabbed: 0
2025-07-31 02:39:57.8|Info|RssSyncService|Starting RSS Sync
2025-07-31 02:39:57.9|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 00:07:58 and 07/31/2025 00:07:58 UTC. Search may be required.
2025-07-31 02:39:57.9|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/31/2025 00:07:58 and 07/31/2025 00:07:58 UTC. Search may be required.
2025-07-31 02:39:57.9|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 00:07:58 and 07/31/2025 00:07:58 UTC. Search may be required.
2025-07-31 02:39:57.9|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 00:07:58 and 07/31/2025 00:07:58 UTC. Search may be required.
2025-07-31 02:39:58.0|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 02:39:58.1|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 02:40:00.0|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 00:08:02 and 07/31/2025 00:08:02 UTC. Search may be required.
2025-07-31 02:40:00.1|Info|DownloadDecisionMaker|Processing 644 releases
2025-07-31 02:40:43.8|Info|RssSyncService|RSS Sync Completed. Reports found: 644, Reports grabbed: 0
2025-07-31 02:55:57.8|Info|RssSyncService|Starting RSS Sync
2025-07-31 02:55:58.5|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 00:07:58 and 07/31/2025 00:55:58 UTC. Search may be required.
2025-07-31 02:55:58.5|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/31/2025 00:07:58 and 07/31/2025 00:55:58 UTC. Search may be required.
2025-07-31 02:55:58.6|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 00:07:58 and 07/31/2025 00:55:58 UTC. Search may be required.
2025-07-31 02:55:58.7|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 00:07:58 and 07/31/2025 00:55:58 UTC. Search may be required.
2025-07-31 02:55:58.9|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/30/2025 23:45:58 and 07/31/2025 00:47:58 UTC. Search may be required.
2025-07-31 02:56:00.0|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 07/30/2025 23:34:58 and 07/30/2025 23:55:58 UTC. Search may be required.
2025-07-31 02:56:02.6|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 00:08:02 and 07/31/2025 00:56:02 UTC. Search may be required.
2025-07-31 02:56:05.2|Info|DownloadDecisionMaker|Processing 744 releases
2025-07-31 02:56:55.5|Info|RssSyncService|RSS Sync Completed. Reports found: 744, Reports grabbed: 0
2025-07-31 03:11:57.9|Info|RssSyncService|Starting RSS Sync
2025-07-31 03:11:57.9|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 00:55:58 and 07/31/2025 00:55:58 UTC. Search may be required.
2025-07-31 03:11:57.9|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 00:55:58 and 07/31/2025 00:55:58 UTC. Search may be required.
2025-07-31 03:11:58.0|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/31/2025 00:55:58 and 07/31/2025 00:55:58 UTC. Search may be required.
2025-07-31 03:11:58.0|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 00:55:58 and 07/31/2025 00:55:58 UTC. Search may be required.
2025-07-31 03:12:00.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 00:56:02 and 07/31/2025 00:56:02 UTC. Search may be required.
2025-07-31 03:12:00.2|Info|DownloadDecisionMaker|Processing 744 releases
2025-07-31 03:12:52.9|Info|RssSyncService|RSS Sync Completed. Reports found: 744, Reports grabbed: 0
2025-07-31 03:27:57.9|Info|RssSyncService|Starting RSS Sync
2025-07-31 03:27:58.0|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 00:55:58 and 07/31/2025 00:55:58 UTC. Search may be required.
2025-07-31 03:27:58.0|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 00:55:58 and 07/31/2025 00:55:58 UTC. Search may be required.
2025-07-31 03:27:58.0|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/31/2025 00:55:58 and 07/31/2025 00:55:58 UTC. Search may be required.
2025-07-31 03:27:58.0|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 00:55:58 and 07/31/2025 00:55:58 UTC. Search may be required.
2025-07-31 03:28:00.2|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 00:56:02 and 07/31/2025 00:56:02 UTC. Search may be required.
2025-07-31 03:28:00.2|Info|DownloadDecisionMaker|Processing 744 releases
2025-07-31 03:28:50.9|Info|RssSyncService|RSS Sync Completed. Reports found: 744, Reports grabbed: 0
2025-07-31 03:43:58.0|Info|RssSyncService|Starting RSS Sync
2025-07-31 03:43:58.3|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/31/2025 00:55:58 and 07/31/2025 01:43:58 UTC. Search may be required.
2025-07-31 03:43:58.4|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 00:55:58 and 07/31/2025 01:43:58 UTC. Search may be required.
2025-07-31 03:43:58.4|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 00:55:58 and 07/31/2025 01:43:58 UTC. Search may be required.
2025-07-31 03:43:58.6|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 00:55:58 and 07/31/2025 01:43:58 UTC. Search may be required.
2025-07-31 03:44:02.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 00:56:02 and 07/31/2025 01:44:02 UTC. Search may be required.
2025-07-31 03:44:04.8|Info|DownloadDecisionMaker|Processing 744 releases
2025-07-31 03:44:55.1|Info|RssSyncService|RSS Sync Completed. Reports found: 744, Reports grabbed: 0
2025-07-31 03:59:58.0|Info|RssSyncService|Starting RSS Sync
2025-07-31 03:59:58.1|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 01:43:58 and 07/31/2025 01:43:58 UTC. Search may be required.
2025-07-31 03:59:58.1|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 01:43:58 and 07/31/2025 01:43:58 UTC. Search may be required.
2025-07-31 03:59:58.1|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/31/2025 01:43:58 and 07/31/2025 01:43:58 UTC. Search may be required.
2025-07-31 03:59:58.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 01:43:58 and 07/31/2025 01:43:58 UTC. Search may be required.
2025-07-31 04:00:00.3|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 01:44:02 and 07/31/2025 01:44:02 UTC. Search may be required.
2025-07-31 04:00:00.3|Info|DownloadDecisionMaker|Processing 744 releases
2025-07-31 04:00:50.1|Info|RssSyncService|RSS Sync Completed. Reports found: 744, Reports grabbed: 0
2025-07-31 04:15:58.0|Info|RssSyncService|Starting RSS Sync
2025-07-31 04:15:58.1|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 01:43:58 and 07/31/2025 01:43:58 UTC. Search may be required.
2025-07-31 04:15:58.1|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/31/2025 01:43:58 and 07/31/2025 01:43:58 UTC. Search may be required.
2025-07-31 04:15:58.1|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 01:43:58 and 07/31/2025 01:43:58 UTC. Search may be required.
2025-07-31 04:15:58.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 01:43:58 and 07/31/2025 01:43:58 UTC. Search may be required.
2025-07-31 04:16:00.3|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 01:44:02 and 07/31/2025 01:44:02 UTC. Search may be required.
2025-07-31 04:16:00.3|Info|DownloadDecisionMaker|Processing 744 releases
2025-07-31 04:16:51.4|Info|RssSyncService|RSS Sync Completed. Reports found: 744, Reports grabbed: 0
2025-07-31 04:31:58.1|Info|RssSyncService|Starting RSS Sync
2025-07-31 04:31:58.4|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 04:31:58.5|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 04:31:58.7|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 01:43:58 and 07/31/2025 02:31:58 UTC. Search may be required.
2025-07-31 04:31:58.8|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/31/2025 01:43:58 and 07/31/2025 02:31:58 UTC. Search may be required.
2025-07-31 04:31:58.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 01:43:58 and 07/31/2025 02:31:58 UTC. Search may be required.
2025-07-31 04:31:58.8|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 01:43:58 and 07/31/2025 02:31:58 UTC. Search may be required.
2025-07-31 04:32:02.9|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 01:44:02 and 07/31/2025 02:32:02 UTC. Search may be required.
2025-07-31 04:32:05.1|Info|DownloadDecisionMaker|Processing 644 releases
2025-07-31 04:32:48.1|Info|RssSyncService|RSS Sync Completed. Reports found: 644, Reports grabbed: 0
2025-07-31 04:47:58.1|Info|RssSyncService|Starting RSS Sync
2025-07-31 04:47:58.2|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 02:31:58 and 07/31/2025 02:31:58 UTC. Search may be required.
2025-07-31 04:47:58.2|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 02:31:58 and 07/31/2025 02:31:58 UTC. Search may be required.
2025-07-31 04:47:58.2|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/31/2025 02:31:58 and 07/31/2025 02:31:58 UTC. Search may be required.
2025-07-31 04:47:58.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 02:31:58 and 07/31/2025 02:31:58 UTC. Search may be required.
2025-07-31 04:48:00.4|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 07/31/2025 01:42:58 and 07/31/2025 01:47:58 UTC. Search may be required.
2025-07-31 04:48:00.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 02:32:02 and 07/31/2025 02:32:02 UTC. Search may be required.
2025-07-31 04:48:00.4|Info|DownloadDecisionMaker|Processing 744 releases
2025-07-31 04:48:51.5|Info|RssSyncService|RSS Sync Completed. Reports found: 744, Reports grabbed: 0
2025-07-31 05:03:58.2|Info|RssSyncService|Starting RSS Sync
2025-07-31 05:03:58.2|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 02:31:58 and 07/31/2025 02:31:58 UTC. Search may be required.
2025-07-31 05:03:58.3|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 02:31:58 and 07/31/2025 02:31:58 UTC. Search may be required.
2025-07-31 05:03:58.3|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/31/2025 02:31:58 and 07/31/2025 02:31:58 UTC. Search may be required.
2025-07-31 05:03:58.3|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 02:31:58 and 07/31/2025 02:31:58 UTC. Search may be required.
2025-07-31 05:04:00.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 02:32:02 and 07/31/2025 02:32:02 UTC. Search may be required.
2025-07-31 05:04:00.5|Info|DownloadDecisionMaker|Processing 744 releases
2025-07-31 05:04:51.4|Info|RssSyncService|RSS Sync Completed. Reports found: 744, Reports grabbed: 0
2025-07-31 05:15:58.2|Info|SceneMappingService|Updating Scene mappings
2025-07-31 05:19:58.2|Info|RssSyncService|Starting RSS Sync
2025-07-31 05:19:58.5|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/31/2025 02:31:58 and 07/31/2025 03:19:58 UTC. Search may be required.
2025-07-31 05:19:58.6|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 02:31:58 and 07/31/2025 03:19:58 UTC. Search may be required.
2025-07-31 05:19:58.7|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 02:31:58 and 07/31/2025 03:19:58 UTC. Search may be required.
2025-07-31 05:19:58.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 02:31:58 and 07/31/2025 03:19:58 UTC. Search may be required.
2025-07-31 05:20:02.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 02:32:02 and 07/31/2025 03:20:02 UTC. Search may be required.
2025-07-31 05:20:05.1|Info|DownloadDecisionMaker|Processing 744 releases
2025-07-31 05:20:54.6|Info|RssSyncService|RSS Sync Completed. Reports found: 744, Reports grabbed: 0
2025-07-31 05:35:58.3|Info|RssSyncService|Starting RSS Sync
2025-07-31 05:35:58.4|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 03:19:58 and 07/31/2025 03:19:58 UTC. Search may be required.
2025-07-31 05:35:58.4|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/31/2025 03:19:58 and 07/31/2025 03:19:58 UTC. Search may be required.
2025-07-31 05:35:58.4|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 03:19:58 and 07/31/2025 03:19:58 UTC. Search may be required.
2025-07-31 05:35:58.4|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 03:19:58 and 07/31/2025 03:19:58 UTC. Search may be required.
2025-07-31 05:36:00.5|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 03:20:02 and 07/31/2025 03:20:02 UTC. Search may be required.
2025-07-31 05:36:00.5|Info|DownloadDecisionMaker|Processing 744 releases
2025-07-31 05:36:51.7|Info|RssSyncService|RSS Sync Completed. Reports found: 744, Reports grabbed: 0
2025-07-31 05:51:58.3|Info|RssSyncService|Starting RSS Sync
2025-07-31 05:51:58.4|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 03:19:58 and 07/31/2025 03:19:58 UTC. Search may be required.
2025-07-31 05:51:58.4|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 03:19:58 and 07/31/2025 03:19:58 UTC. Search may be required.
2025-07-31 05:51:58.4|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/31/2025 03:19:58 and 07/31/2025 03:19:58 UTC. Search may be required.
2025-07-31 05:51:58.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 03:19:58 and 07/31/2025 03:19:58 UTC. Search may be required.
2025-07-31 05:52:00.5|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 03:20:02 and 07/31/2025 03:20:02 UTC. Search may be required.
2025-07-31 05:52:00.6|Info|DownloadDecisionMaker|Processing 744 releases
2025-07-31 05:52:51.7|Info|RssSyncService|RSS Sync Completed. Reports found: 744, Reports grabbed: 0
2025-07-31 06:07:58.4|Info|RssSyncService|Starting RSS Sync
2025-07-31 06:07:58.7|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 03:19:58 and 07/31/2025 04:07:58 UTC. Search may be required.
2025-07-31 06:07:58.7|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/31/2025 03:19:58 and 07/31/2025 04:07:58 UTC. Search may be required.
2025-07-31 06:07:58.7|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 03:19:58 and 07/31/2025 04:07:58 UTC. Search may be required.
2025-07-31 06:07:59.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 03:19:58 and 07/31/2025 04:07:59 UTC. Search may be required.
2025-07-31 06:07:59.1|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/31/2025 03:02:58 and 07/31/2025 03:42:59 UTC. Search may be required.
2025-07-31 06:08:03.2|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 03:20:02 and 07/31/2025 04:08:03 UTC. Search may be required.
2025-07-31 06:08:05.5|Info|DownloadDecisionMaker|Processing 744 releases
2025-07-31 06:08:55.6|Info|RssSyncService|RSS Sync Completed. Reports found: 744, Reports grabbed: 0
2025-07-31 06:23:58.4|Info|RssSyncService|Starting RSS Sync
2025-07-31 06:23:58.5|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 04:07:58 and 07/31/2025 04:07:58 UTC. Search may be required.
2025-07-31 06:23:58.5|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 04:07:58 and 07/31/2025 04:07:58 UTC. Search may be required.
2025-07-31 06:23:58.5|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/31/2025 04:07:58 and 07/31/2025 04:07:58 UTC. Search may be required.
2025-07-31 06:23:58.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 04:07:59 and 07/31/2025 04:07:59 UTC. Search may be required.
2025-07-31 06:23:58.7|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 06:23:58.7|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 06:24:00.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 04:08:03 and 07/31/2025 04:08:03 UTC. Search may be required.
2025-07-31 06:24:00.7|Info|DownloadDecisionMaker|Processing 644 releases
2025-07-31 06:24:45.0|Info|RssSyncService|RSS Sync Completed. Reports found: 644, Reports grabbed: 0
2025-07-31 06:39:58.5|Info|RssSyncService|Starting RSS Sync
2025-07-31 06:39:58.5|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 04:07:58 and 07/31/2025 04:07:58 UTC. Search may be required.
2025-07-31 06:39:58.6|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/31/2025 04:07:58 and 07/31/2025 04:07:58 UTC. Search may be required.
2025-07-31 06:39:58.6|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 04:07:58 and 07/31/2025 04:07:58 UTC. Search may be required.
2025-07-31 06:39:58.6|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 04:07:59 and 07/31/2025 04:07:59 UTC. Search may be required.
2025-07-31 06:39:58.7|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 06:39:58.7|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 06:40:00.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 04:08:03 and 07/31/2025 04:08:03 UTC. Search may be required.
2025-07-31 06:40:00.7|Info|DownloadDecisionMaker|Processing 644 releases
2025-07-31 06:40:42.7|Info|RssSyncService|RSS Sync Completed. Reports found: 644, Reports grabbed: 0
2025-07-31 06:55:58.5|Info|RssSyncService|Starting RSS Sync
2025-07-31 06:55:59.0|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3976 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Network unreachable&#xA; ---&gt; System.Net.Http.HttpRequestException: Network unreachable (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (101): Network unreachable&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 06:55:59.0|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 06:55:59.1|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 04:07:58 and 07/31/2025 04:55:59 UTC. Search may be required.
2025-07-31 06:55:59.3|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/31/2025 04:07:58 and 07/31/2025 04:55:59 UTC. Search may be required.
2025-07-31 06:55:59.6|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 04:07:59 and 07/31/2025 04:55:59 UTC. Search may be required.
2025-07-31 06:55:59.7|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 04:07:58 and 07/31/2025 04:55:59 UTC. Search may be required.
2025-07-31 06:55:59.7|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/31/2025 04:07:59 and 07/31/2025 04:52:59 UTC. Search may be required.
2025-07-31 06:56:00.6|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 07/31/2025 03:34:58 and 07/31/2025 03:55:59 UTC. Search may be required.
2025-07-31 06:56:03.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 04:08:03 and 07/31/2025 04:56:03 UTC. Search may be required.
2025-07-31 06:56:05.9|Info|DownloadDecisionMaker|Processing 729 releases
2025-07-31 06:56:52.0|Info|RssSyncService|RSS Sync Completed. Reports found: 729, Reports grabbed: 0
2025-07-31 07:11:58.5|Info|RssSyncService|Starting RSS Sync
2025-07-31 07:11:58.6|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 04:55:59 and 07/31/2025 04:55:59 UTC. Search may be required.
2025-07-31 07:11:58.6|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 04:55:59 and 07/31/2025 04:55:59 UTC. Search may be required.
2025-07-31 07:11:58.6|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/31/2025 04:55:59 and 07/31/2025 04:55:59 UTC. Search may be required.
2025-07-31 07:11:58.6|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 04:55:59 and 07/31/2025 04:55:59 UTC. Search may be required.
2025-07-31 07:11:58.8|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 07:11:58.8|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 07:12:00.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 04:56:03 and 07/31/2025 04:56:03 UTC. Search may be required.
2025-07-31 07:12:00.8|Info|DownloadDecisionMaker|Processing 729 releases
2025-07-31 07:12:50.8|Info|RssSyncService|RSS Sync Completed. Reports found: 729, Reports grabbed: 0
2025-07-31 07:27:58.6|Info|RssSyncService|Starting RSS Sync
2025-07-31 07:27:58.6|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 04:55:59 and 07/31/2025 04:55:59 UTC. Search may be required.
2025-07-31 07:27:58.7|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 04:55:59 and 07/31/2025 04:55:59 UTC. Search may be required.
2025-07-31 07:27:58.7|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/31/2025 04:55:59 and 07/31/2025 04:55:59 UTC. Search may be required.
2025-07-31 07:27:58.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 04:55:59 and 07/31/2025 04:55:59 UTC. Search may be required.
2025-07-31 07:28:00.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 04:56:03 and 07/31/2025 04:56:03 UTC. Search may be required.
2025-07-31 07:28:00.8|Info|DownloadDecisionMaker|Processing 744 releases
2025-07-31 07:28:51.8|Info|RssSyncService|RSS Sync Completed. Reports found: 744, Reports grabbed: 0
2025-07-31 07:43:58.6|Info|RssSyncService|Starting RSS Sync
2025-07-31 07:43:58.8|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 07:43:58.9|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 07:43:58.9|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 04:55:59 and 07/31/2025 05:43:58 UTC. Search may be required.
2025-07-31 07:43:59.0|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/31/2025 04:55:59 and 07/31/2025 05:43:59 UTC. Search may be required.
2025-07-31 07:43:59.0|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 04:55:59 and 07/31/2025 05:43:59 UTC. Search may be required.
2025-07-31 07:43:59.1|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/31/2025 04:55:59 and 07/31/2025 05:01:59 UTC. Search may be required.
2025-07-31 07:43:59.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 04:55:59 and 07/31/2025 05:43:59 UTC. Search may be required.
2025-07-31 07:44:02.9|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 04:56:03 and 07/31/2025 05:44:02 UTC. Search may be required.
2025-07-31 07:44:05.7|Info|DownloadDecisionMaker|Processing 644 releases
2025-07-31 07:44:48.2|Info|RssSyncService|RSS Sync Completed. Reports found: 644, Reports grabbed: 0
2025-07-31 07:59:58.7|Info|RssSyncService|Starting RSS Sync
2025-07-31 07:59:58.7|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 05:43:58 and 07/31/2025 05:43:59 UTC. Search may be required.
2025-07-31 07:59:58.8|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/31/2025 05:43:59 and 07/31/2025 05:43:59 UTC. Search may be required.
2025-07-31 07:59:58.8|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 05:43:59 and 07/31/2025 05:43:59 UTC. Search may be required.
2025-07-31 07:59:58.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 05:43:59 and 07/31/2025 05:43:59 UTC. Search may be required.
2025-07-31 08:00:00.9|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 07/31/2025 04:50:59 and 07/31/2025 04:59:59 UTC. Search may be required.
2025-07-31 08:00:00.9|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 05:44:02 and 07/31/2025 05:44:02 UTC. Search may be required.
2025-07-31 08:00:00.9|Info|DownloadDecisionMaker|Processing 744 releases
2025-07-31 08:00:50.7|Info|RssSyncService|RSS Sync Completed. Reports found: 744, Reports grabbed: 0
2025-07-31 08:15:58.7|Info|RssSyncService|Starting RSS Sync
2025-07-31 08:15:58.8|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/31/2025 05:43:59 and 07/31/2025 05:43:59 UTC. Search may be required.
2025-07-31 08:15:58.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 05:43:59 and 07/31/2025 05:43:59 UTC. Search may be required.
2025-07-31 08:15:58.8|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/31/2025 05:43:59 and 07/31/2025 05:43:59 UTC. Search may be required.
2025-07-31 08:15:58.9|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 05:43:59 and 07/31/2025 05:43:59 UTC. Search may be required.
2025-07-31 08:16:00.9|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 05:44:02 and 07/31/2025 05:44:02 UTC. Search may be required.
2025-07-31 08:16:00.9|Info|DownloadDecisionMaker|Processing 744 releases
2025-07-31 08:16:28.7|Info|SceneMappingService|Updating Scene mappings
2025-07-31 08:16:53.2|Info|RssSyncService|RSS Sync Completed. Reports found: 744, Reports grabbed: 0
2025-07-31 08:31:58.7|Info|RssSyncService|Starting RSS Sync
2025-07-31 08:31:59.2|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 05:43:59 and 07/31/2025 06:31:59 UTC. Search may be required.
2025-07-31 08:31:59.2|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/31/2025 05:43:59 and 07/31/2025 06:06:59 UTC. Search may be required.
2025-07-31 08:31:59.4|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 05:43:59 and 07/31/2025 06:31:59 UTC. Search may be required.
2025-07-31 08:32:03.2|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 05:44:02 and 07/31/2025 06:32:03 UTC. Search may be required.
2025-07-31 08:32:05.7|Info|DownloadDecisionMaker|Processing 752 releases
2025-07-31 08:32:57.6|Info|RssSyncService|RSS Sync Completed. Reports found: 752, Reports grabbed: 0
2025-07-31 08:47:58.8|Info|RssSyncService|Starting RSS Sync
2025-07-31 08:47:58.8|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 06:31:59 and 07/31/2025 06:31:59 UTC. Search may be required.
2025-07-31 08:47:58.9|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 06:31:59 and 07/29/2025 06:31:59 UTC. Search may be required.
2025-07-31 08:47:58.9|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 06:31:59 and 07/29/2025 06:31:59 UTC. Search may be required.
2025-07-31 08:47:58.9|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 06:31:59 and 07/31/2025 06:31:59 UTC. Search may be required.
2025-07-31 08:48:01.0|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 06:32:03 and 07/31/2025 06:32:03 UTC. Search may be required.
2025-07-31 08:48:01.0|Info|DownloadDecisionMaker|Processing 752 releases
2025-07-31 08:48:53.6|Info|RssSyncService|RSS Sync Completed. Reports found: 752, Reports grabbed: 0
2025-07-31 09:03:58.8|Info|RssSyncService|Starting RSS Sync
2025-07-31 09:03:58.9|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 06:31:59 and 07/31/2025 06:31:59 UTC. Search may be required.
2025-07-31 09:03:58.9|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 06:31:59 and 07/29/2025 06:31:59 UTC. Search may be required.
2025-07-31 09:03:58.9|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 06:31:59 and 07/29/2025 06:31:59 UTC. Search may be required.
2025-07-31 09:03:58.9|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 06:31:59 and 07/31/2025 06:31:59 UTC. Search may be required.
2025-07-31 09:04:01.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 06:32:03 and 07/31/2025 06:32:03 UTC. Search may be required.
2025-07-31 09:04:01.1|Info|DownloadDecisionMaker|Processing 752 releases
2025-07-31 09:04:51.4|Info|RssSyncService|RSS Sync Completed. Reports found: 752, Reports grabbed: 0
2025-07-31 09:19:58.9|Info|RssSyncService|Starting RSS Sync
2025-07-31 09:19:59.6|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 09:19:59.6|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 09:20:00.5|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 06:31:59 and 07/29/2025 07:20:00 UTC. Search may be required.
2025-07-31 09:20:00.6|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 06:31:59 and 07/29/2025 07:20:00 UTC. Search may be required.
2025-07-31 09:20:01.0|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 06:31:59 and 07/31/2025 07:20:00 UTC. Search may be required.
2025-07-31 09:20:01.1|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 06:31:59 and 07/31/2025 07:20:00 UTC. Search may be required.
2025-07-31 09:20:01.1|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 07/31/2025 06:47:59 and 07/31/2025 06:55:00 UTC. Search may be required.
2025-07-31 09:20:05.0|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 06:32:03 and 07/31/2025 07:20:05 UTC. Search may be required.
2025-07-31 09:20:06.4|Info|DownloadDecisionMaker|Processing 737 releases
2025-07-31 09:20:56.5|Info|RssSyncService|RSS Sync Completed. Reports found: 737, Reports grabbed: 0
2025-07-31 09:35:58.9|Info|RssSyncService|Starting RSS Sync
2025-07-31 09:35:59.0|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 07:20:00 and 07/29/2025 07:20:00 UTC. Search may be required.
2025-07-31 09:35:59.0|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 07:20:00 and 07/29/2025 07:20:00 UTC. Search may be required.
2025-07-31 09:35:59.0|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 07:20:00 and 07/31/2025 07:20:00 UTC. Search may be required.
2025-07-31 09:35:59.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 07:20:00 and 07/31/2025 07:20:00 UTC. Search may be required.
2025-07-31 09:36:01.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 07:20:05 and 07/31/2025 07:20:05 UTC. Search may be required.
2025-07-31 09:36:01.2|Info|DownloadDecisionMaker|Processing 752 releases
2025-07-31 09:36:53.6|Info|RssSyncService|RSS Sync Completed. Reports found: 752, Reports grabbed: 0
2025-07-31 09:51:59.0|Info|RssSyncService|Starting RSS Sync
2025-07-31 09:51:59.1|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 07:20:00 and 07/29/2025 07:20:00 UTC. Search may be required.
2025-07-31 09:51:59.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 07:20:00 and 07/31/2025 07:20:00 UTC. Search may be required.
2025-07-31 09:51:59.1|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 07:20:00 and 07/29/2025 07:20:00 UTC. Search may be required.
2025-07-31 09:51:59.3|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 07:20:00 and 07/31/2025 07:20:00 UTC. Search may be required.
2025-07-31 09:52:01.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 07:20:05 and 07/31/2025 07:20:05 UTC. Search may be required.
2025-07-31 09:52:01.2|Info|DownloadDecisionMaker|Processing 752 releases
2025-07-31 09:52:53.9|Info|RssSyncService|RSS Sync Completed. Reports found: 752, Reports grabbed: 0
2025-07-31 09:57:29.0|Info|RefreshSeriesService|Skipping refresh of series: Better Call Saul
2025-07-31 09:57:29.0|Info|DiskScanService|Scanning Better Call Saul
2025-07-31 09:57:29.1|Info|DiskScanService|Completed scanning disk for Better Call Saul
2025-07-31 09:57:29.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 09:57:29.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 09:57:29.2|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 09:57:29.2|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 09:57:29.2|Info|RefreshSeriesService|Skipping refresh of series: The Big Bang Theory
2025-07-31 09:57:29.3|Info|DiskScanService|Scanning The Big Bang Theory
2025-07-31 09:57:29.4|Info|DiskScanService|Completed scanning disk for The Big Bang Theory
2025-07-31 09:57:29.4|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 09:57:29.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 09:57:29.5|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 09:57:29.5|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 09:57:29.5|Info|RefreshSeriesService|Updating Black Mirror
2025-07-31 09:57:29.8|Info|RefreshEpisodeService|Starting episode info refresh for: [253463][Black Mirror]
2025-07-31 09:57:30.1|Info|RefreshEpisodeService|Finished episode refresh for series: [253463][Black Mirror].
2025-07-31 09:57:30.2|Info|DiskScanService|Scanning Black Mirror
2025-07-31 09:57:30.5|Info|DiskScanService|Completed scanning disk for Black Mirror
2025-07-31 09:57:30.5|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 09:57:30.5|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 09:57:30.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 09:57:30.6|Info|ExistingExtraFileService|Found 1 possible extra files, imported 1 files.
2025-07-31 09:57:30.6|Info|RefreshSeriesService|Updating The Boys
2025-07-31 09:57:30.7|Info|RefreshEpisodeService|Starting episode info refresh for: [355567][The Boys]
2025-07-31 09:57:31.1|Info|RefreshEpisodeService|Finished episode refresh for series: [355567][The Boys].
2025-07-31 09:57:31.1|Info|DiskScanService|Scanning The Boys
2025-07-31 09:57:31.2|Info|DiskScanService|Completed scanning disk for The Boys
2025-07-31 09:57:31.3|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 09:57:31.3|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 09:57:31.4|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 09:57:31.4|Info|ExistingExtraFileService|Found 2 possible extra files, imported 1 files.
2025-07-31 09:57:31.4|Info|RefreshSeriesService|Skipping refresh of series: Breaking Bad
2025-07-31 09:57:31.5|Info|DiskScanService|Scanning Breaking Bad
2025-07-31 09:57:31.6|Info|DiskScanService|Completed scanning disk for Breaking Bad
2025-07-31 09:57:31.6|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 09:57:31.6|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 09:57:31.6|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 09:57:31.6|Info|ExistingExtraFileService|Found 1 possible extra files, imported 1 files.
2025-07-31 09:57:31.7|Info|RefreshSeriesService|Skipping refresh of series: Dark
2025-07-31 09:57:31.7|Info|DiskScanService|Scanning Dark
2025-07-31 09:57:31.7|Info|DiskScanService|Completed scanning disk for Dark
2025-07-31 09:57:31.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 09:57:31.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 09:57:31.8|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 09:57:31.8|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 09:57:31.8|Info|RefreshSeriesService|Skipping refresh of series: Dexter
2025-07-31 09:57:31.9|Info|DiskScanService|Scanning Dexter
2025-07-31 09:57:32.6|Info|DiskScanService|Completed scanning disk for Dexter
2025-07-31 09:57:32.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 09:57:32.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 09:57:33.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 09:57:33.0|Info|ExistingExtraFileService|Found 2 possible extra files, imported 0 files.
2025-07-31 09:57:33.1|Info|RefreshSeriesService|Skipping refresh of series: Dragon Ball
2025-07-31 09:57:33.1|Info|DiskScanService|Scanning Dragon Ball
2025-07-31 09:57:34.2|Info|DiskScanService|Completed scanning disk for Dragon Ball
2025-07-31 09:57:34.6|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 09:57:34.7|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 09:57:35.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 09:57:35.3|Info|ExistingExtraFileService|Found 174 possible extra files, imported 167 files.
2025-07-31 09:57:35.9|Info|RefreshSeriesService|Skipping refresh of series: Friends
2025-07-31 09:57:35.9|Info|DiskScanService|Scanning Friends
2025-07-31 09:57:36.0|Info|DiskScanService|Completed scanning disk for Friends
2025-07-31 09:57:36.0|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 09:57:36.0|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 09:57:36.0|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 09:57:36.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 09:57:36.1|Info|RefreshSeriesService|Skipping refresh of series: Game of Thrones
2025-07-31 09:57:36.1|Info|DiskScanService|Scanning Game of Thrones
2025-07-31 09:57:36.2|Info|DiskScanService|Completed scanning disk for Game of Thrones
2025-07-31 09:57:36.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 09:57:36.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 09:57:36.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 09:57:36.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 09:57:36.3|Info|RefreshSeriesService|Updating House of the Dragon
2025-07-31 09:57:36.3|Info|RefreshEpisodeService|Starting episode info refresh for: [371572][House of the Dragon]
2025-07-31 09:57:36.6|Info|RefreshEpisodeService|Finished episode refresh for series: [371572][House of the Dragon].
2025-07-31 09:57:36.6|Info|DiskScanService|Scanning House of the Dragon
2025-07-31 09:57:36.7|Info|DiskScanService|Completed scanning disk for House of the Dragon
2025-07-31 09:57:36.7|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 09:57:36.7|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 09:57:36.7|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 09:57:36.8|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 09:57:36.8|Info|RefreshSeriesService|Updating Outlander
2025-07-31 09:57:36.8|Info|RefreshEpisodeService|Starting episode info refresh for: [270408][Outlander]
2025-07-31 09:57:37.5|Info|RefreshEpisodeService|Finished episode refresh for series: [270408][Outlander].
2025-07-31 09:57:37.5|Info|DiskScanService|Scanning Outlander
2025-07-31 09:57:37.7|Info|DiskScanService|Completed scanning disk for Outlander
2025-07-31 09:57:37.7|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 09:57:37.7|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 09:57:37.7|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 09:57:37.7|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 09:57:37.8|Info|RefreshSeriesService|Skipping refresh of series: Peaky Blinders
2025-07-31 09:57:37.8|Info|DiskScanService|Scanning Peaky Blinders
2025-07-31 09:57:37.9|Info|DiskScanService|Completed scanning disk for Peaky Blinders
2025-07-31 09:57:37.9|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 09:57:37.9|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 09:57:37.9|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 09:57:37.9|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 09:57:38.0|Info|RefreshSeriesService|Skipping refresh of series: Prison Break
2025-07-31 09:57:38.0|Info|DiskScanService|Scanning Prison Break
2025-07-31 09:57:38.1|Info|DiskScanService|Completed scanning disk for Prison Break
2025-07-31 09:57:38.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 09:57:38.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 09:57:38.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 09:57:38.1|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 09:57:38.2|Info|RefreshSeriesService|Updating Rick and Morty
2025-07-31 09:57:38.2|Info|RefreshEpisodeService|Starting episode info refresh for: [275274][Rick and Morty]
2025-07-31 09:57:39.0|Info|RefreshEpisodeService|Finished episode refresh for series: [275274][Rick and Morty].
2025-07-31 09:57:39.0|Info|DiskScanService|Scanning Rick and Morty
2025-07-31 09:57:39.7|Info|DiskScanService|Completed scanning disk for Rick and Morty
2025-07-31 09:57:39.8|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 09:57:39.8|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 09:57:39.9|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 09:57:39.9|Info|ExistingExtraFileService|Found 1 possible extra files, imported 1 files.
2025-07-31 09:57:39.9|Info|RefreshSeriesService|Skipping refresh of series: Squid Game
2025-07-31 09:57:40.0|Info|DiskScanService|Scanning Squid Game
2025-07-31 09:57:40.1|Info|DiskScanService|Completed scanning disk for Squid Game
2025-07-31 09:57:40.1|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 09:57:40.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 09:57:40.1|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 09:57:40.1|Info|ExistingExtraFileService|Found 1 possible extra files, imported 1 files.
2025-07-31 09:57:40.2|Info|RefreshSeriesService|Updating Stranger Things
2025-07-31 09:57:40.2|Info|RefreshEpisodeService|Starting episode info refresh for: [305288][Stranger Things]
2025-07-31 09:57:40.5|Info|RefreshEpisodeService|Finished episode refresh for series: [305288][Stranger Things].
2025-07-31 09:57:40.6|Info|DiskScanService|Scanning Stranger Things
2025-07-31 09:57:40.6|Info|DiskScanService|Completed scanning disk for Stranger Things
2025-07-31 09:57:40.7|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 09:57:40.7|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 09:57:40.7|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 09:57:40.7|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 10:07:59.0|Info|RssSyncService|Starting RSS Sync
2025-07-31 10:07:59.6|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 07:20:00 and 07/29/2025 08:07:59 UTC. Search may be required.
2025-07-31 10:07:59.6|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 07:20:00 and 07/29/2025 08:07:59 UTC. Search may be required.
2025-07-31 10:07:59.6|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 07:20:00 and 07/31/2025 08:07:59 UTC. Search may be required.
2025-07-31 10:08:00.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 07:20:00 and 07/31/2025 08:07:59 UTC. Search may be required.
2025-07-31 10:08:00.3|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/31/2025 06:52:00 and 07/31/2025 07:46:00 UTC. Search may be required.
2025-07-31 10:08:04.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 07:20:05 and 07/31/2025 08:08:04 UTC. Search may be required.
2025-07-31 10:08:05.8|Info|DownloadDecisionMaker|Processing 752 releases
2025-07-31 10:09:15.5|Info|RssSyncService|RSS Sync Completed. Reports found: 752, Reports grabbed: 0
2025-07-31 10:24:29.1|Info|RssSyncService|Starting RSS Sync
2025-07-31 10:24:29.2|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 08:07:59 and 07/29/2025 08:07:59 UTC. Search may be required.
2025-07-31 10:24:29.2|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 08:07:59 and 07/31/2025 08:07:59 UTC. Search may be required.
2025-07-31 10:24:29.3|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 08:07:59 and 07/29/2025 08:07:59 UTC. Search may be required.
2025-07-31 10:24:29.5|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 10:24:29.5|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 10:24:31.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 08:08:04 and 07/31/2025 08:08:04 UTC. Search may be required.
2025-07-31 10:24:31.4|Info|DownloadDecisionMaker|Processing 737 releases
2025-07-31 10:25:36.2|Info|RssSyncService|RSS Sync Completed. Reports found: 737, Reports grabbed: 0
2025-07-31 10:40:59.2|Info|RssSyncService|Starting RSS Sync
2025-07-31 10:40:59.2|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 08:07:59 and 07/29/2025 08:07:59 UTC. Search may be required.
2025-07-31 10:40:59.3|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 08:07:59 and 07/31/2025 08:07:59 UTC. Search may be required.
2025-07-31 10:40:59.3|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 08:07:59 and 07/29/2025 08:07:59 UTC. Search may be required.
2025-07-31 10:40:59.4|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 10:40:59.5|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 10:41:01.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 08:08:04 and 07/31/2025 08:08:04 UTC. Search may be required.
2025-07-31 10:41:01.4|Info|DownloadDecisionMaker|Processing 737 releases
2025-07-31 10:42:02.6|Info|RssSyncService|RSS Sync Completed. Reports found: 737, Reports grabbed: 0
2025-07-31 10:57:29.2|Info|RssSyncService|Starting RSS Sync
2025-07-31 10:57:30.1|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 08:07:59 and 07/29/2025 08:57:29 UTC. Search may be required.
2025-07-31 10:57:30.1|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 08:07:59 and 07/29/2025 08:57:29 UTC. Search may be required.
2025-07-31 10:57:30.4|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 08:07:59 and 07/31/2025 08:57:29 UTC. Search may be required.
2025-07-31 10:57:31.3|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 08:08:00 and 07/31/2025 08:57:30 UTC. Search may be required.
2025-07-31 10:57:31.5|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 07/31/2025 07:59:59 and 07/31/2025 08:27:30 UTC. Search may be required.
2025-07-31 10:57:31.7|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/31/2025 08:05:00 and 07/31/2025 08:29:31 UTC. Search may be required.
2025-07-31 10:57:34.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 08:08:04 and 07/31/2025 08:57:34 UTC. Search may be required.
2025-07-31 10:57:36.5|Info|DownloadDecisionMaker|Processing 752 releases
2025-07-31 10:58:37.2|Info|RssSyncService|RSS Sync Completed. Reports found: 752, Reports grabbed: 0
2025-07-31 11:13:59.3|Info|RssSyncService|Starting RSS Sync
2025-07-31 11:14:01.3|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 08:57:29 and 07/29/2025 08:57:29 UTC. Search may be required.
2025-07-31 11:14:02.2|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 08:57:34 and 07/31/2025 08:57:34 UTC. Search may be required.
2025-07-31 11:14:02.2|Info|DownloadDecisionMaker|Processing 752 releases
2025-07-31 11:15:07.7|Info|RssSyncService|RSS Sync Completed. Reports found: 752, Reports grabbed: 0
2025-07-31 11:16:59.3|Info|SceneMappingService|Updating Scene mappings
2025-07-31 11:30:29.4|Info|RssSyncService|Starting RSS Sync
2025-07-31 11:30:31.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 08:57:34 and 07/31/2025 08:57:34 UTC. Search may be required.
2025-07-31 11:30:31.9|Info|DownloadDecisionMaker|Processing 752 releases
2025-07-31 11:31:39.7|Info|RssSyncService|RSS Sync Completed. Reports found: 752, Reports grabbed: 0
2025-07-31 11:46:59.5|Info|RssSyncService|Starting RSS Sync
2025-07-31 11:47:02.7|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 08:57:31 and 07/31/2025 09:47:02 UTC. Search may be required.
2025-07-31 11:47:02.7|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 08:57:30 and 07/29/2025 09:47:01 UTC. Search may be required.
2025-07-31 11:47:02.8|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 08:57:30 and 07/29/2025 09:47:01 UTC. Search may be required.
2025-07-31 11:47:03.1|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 08:57:30 and 07/31/2025 09:47:01 UTC. Search may be required.
2025-07-31 11:47:03.5|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/31/2025 08:56:31 and 07/31/2025 09:40:03 UTC. Search may be required.
2025-07-31 11:47:06.0|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 08:57:34 and 07/31/2025 09:47:05 UTC. Search may be required.
2025-07-31 11:47:07.8|Info|DownloadDecisionMaker|Processing 752 releases
2025-07-31 11:48:10.6|Info|RssSyncService|RSS Sync Completed. Reports found: 752, Reports grabbed: 0
2025-07-31 12:03:29.6|Info|RssSyncService|Starting RSS Sync
2025-07-31 12:03:30.0|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 09:47:02 and 07/31/2025 09:47:02 UTC. Search may be required.
2025-07-31 12:03:31.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 09:47:05 and 07/31/2025 09:47:05 UTC. Search may be required.
2025-07-31 12:03:31.9|Info|DownloadDecisionMaker|Processing 752 releases
2025-07-31 12:04:34.3|Info|RssSyncService|RSS Sync Completed. Reports found: 752, Reports grabbed: 0
2025-07-31 12:19:59.7|Info|RssSyncService|Starting RSS Sync
2025-07-31 12:20:01.0|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 09:47:02 and 07/31/2025 09:47:02 UTC. Search may be required.
2025-07-31 12:20:01.9|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 09:47:05 and 07/31/2025 09:47:05 UTC. Search may be required.
2025-07-31 12:20:01.9|Info|DownloadDecisionMaker|Processing 752 releases
2025-07-31 12:21:01.4|Info|RssSyncService|RSS Sync Completed. Reports found: 752, Reports grabbed: 0
2025-07-31 12:36:29.7|Info|RssSyncService|Starting RSS Sync
2025-07-31 12:36:30.4|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3976 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Network unreachable&#xA; ---&gt; System.Net.Http.HttpRequestException: Network unreachable (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (101): Network unreachable&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 12:36:30.4|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 09:47:02 and 07/29/2025 10:36:30 UTC. Search may be required.
2025-07-31 12:36:30.6|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 09:47:02 and 07/29/2025 10:36:30 UTC. Search may be required.
2025-07-31 12:36:30.6|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 12:36:30.6|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 12:36:30.7|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 12:36:31.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 09:47:02 and 07/31/2025 10:36:30 UTC. Search may be required.
2025-07-31 12:36:31.2|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 09:47:02 and 07/31/2025 10:36:30 UTC. Search may be required.
2025-07-31 12:36:31.6|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/31/2025 09:45:03 and 07/31/2025 10:14:31 UTC. Search may be required.
2025-07-31 12:36:35.5|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 09:47:05 and 07/31/2025 10:36:35 UTC. Search may be required.
2025-07-31 12:36:37.1|Info|DownloadDecisionMaker|Processing 637 releases
2025-07-31 12:37:27.5|Info|RssSyncService|RSS Sync Completed. Reports found: 637, Reports grabbed: 0
2025-07-31 12:52:29.8|Info|RssSyncService|Starting RSS Sync
2025-07-31 12:52:29.9|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 10:36:30 and 07/31/2025 10:36:30 UTC. Search may be required.
2025-07-31 12:52:29.9|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 10:36:30 and 07/31/2025 10:36:30 UTC. Search may be required.
2025-07-31 12:52:30.0|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 10:36:30 and 07/29/2025 10:36:30 UTC. Search may be required.
2025-07-31 12:52:30.0|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 10:36:30 and 07/29/2025 10:36:30 UTC. Search may be required.
2025-07-31 12:52:30.4|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 12:52:30.4|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 12:52:30.4|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 12:52:30.5|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 12:52:32.0|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 10:36:35 and 07/31/2025 10:36:35 UTC. Search may be required.
2025-07-31 12:52:32.1|Info|DownloadDecisionMaker|Processing 637 releases
2025-07-31 12:53:21.7|Info|RssSyncService|RSS Sync Completed. Reports found: 637, Reports grabbed: 0
2025-07-31 13:08:29.8|Info|RssSyncService|Starting RSS Sync
2025-07-31 13:08:29.9|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 10:36:30 and 07/31/2025 10:36:30 UTC. Search may be required.
2025-07-31 13:08:29.9|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 10:36:30 and 07/29/2025 10:36:30 UTC. Search may be required.
2025-07-31 13:08:29.9|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 10:36:30 and 07/29/2025 10:36:30 UTC. Search may be required.
2025-07-31 13:08:30.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 10:36:30 and 07/31/2025 10:36:30 UTC. Search may be required.
2025-07-31 13:08:32.1|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 07/31/2025 09:46:02 and 07/31/2025 10:20:30 UTC. Search may be required.
2025-07-31 13:08:32.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 10:36:35 and 07/31/2025 10:36:35 UTC. Search may be required.
2025-07-31 13:08:32.2|Info|DownloadDecisionMaker|Processing 752 releases
2025-07-31 13:09:31.3|Info|RssSyncService|RSS Sync Completed. Reports found: 752, Reports grabbed: 0
2025-07-31 13:24:59.9|Info|RssSyncService|Starting RSS Sync
2025-07-31 13:25:00.5|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 10:36:30 and 07/29/2025 11:25:00 UTC. Search may be required.
2025-07-31 13:25:00.5|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 10:36:30 and 07/29/2025 11:25:00 UTC. Search may be required.
2025-07-31 13:25:00.6|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 10:36:30 and 07/31/2025 11:25:00 UTC. Search may be required.
2025-07-31 13:25:00.7|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 10:36:30 and 07/31/2025 11:25:00 UTC. Search may be required.
2025-07-31 13:25:04.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 10:36:35 and 07/31/2025 11:25:04 UTC. Search may be required.
2025-07-31 13:25:06.8|Info|DownloadDecisionMaker|Processing 752 releases
2025-07-31 13:26:04.0|Info|RssSyncService|RSS Sync Completed. Reports found: 752, Reports grabbed: 0
2025-07-31 13:41:29.9|Info|RssSyncService|Starting RSS Sync
2025-07-31 13:41:30.1|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 11:25:00 and 07/31/2025 11:25:00 UTC. Search may be required.
2025-07-31 13:41:30.2|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 11:25:00 and 07/29/2025 11:25:00 UTC. Search may be required.
2025-07-31 13:41:30.2|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 11:25:00 and 07/29/2025 11:25:00 UTC. Search may be required.
2025-07-31 13:41:30.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 11:25:00 and 07/31/2025 11:25:00 UTC. Search may be required.
2025-07-31 13:41:32.2|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 11:25:04 and 07/31/2025 11:25:04 UTC. Search may be required.
2025-07-31 13:41:32.2|Info|DownloadDecisionMaker|Processing 752 releases
2025-07-31 13:42:31.4|Info|RssSyncService|RSS Sync Completed. Reports found: 752, Reports grabbed: 0
2025-07-31 13:58:00.0|Info|RssSyncService|Starting RSS Sync
2025-07-31 13:58:00.7|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 11:25:00 and 07/29/2025 11:25:00 UTC. Search may be required.
2025-07-31 13:58:00.8|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 11:25:00 and 07/31/2025 11:25:00 UTC. Search may be required.
2025-07-31 13:58:00.8|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 11:25:00 and 07/29/2025 11:25:00 UTC. Search may be required.
2025-07-31 13:58:01.4|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 11:25:00 and 07/31/2025 11:25:00 UTC. Search may be required.
2025-07-31 13:58:02.3|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 11:25:04 and 07/31/2025 11:25:04 UTC. Search may be required.
2025-07-31 13:58:02.6|Info|DownloadDecisionMaker|Processing 752 releases
2025-07-31 13:59:03.0|Info|RssSyncService|RSS Sync Completed. Reports found: 752, Reports grabbed: 0
2025-07-31 14:14:30.1|Info|RssSyncService|Starting RSS Sync
2025-07-31 14:14:32.6|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 11:25:00 and 07/29/2025 12:14:32 UTC. Search may be required.
2025-07-31 14:14:32.7|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 11:25:00 and 07/29/2025 12:14:32 UTC. Search may be required.
2025-07-31 14:14:33.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 11:25:00 and 07/31/2025 12:14:33 UTC. Search may be required.
2025-07-31 14:14:33.3|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 11:25:00 and 07/31/2025 12:14:32 UTC. Search may be required.
2025-07-31 14:14:33.7|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/31/2025 11:10:00 and 07/31/2025 12:04:33 UTC. Search may be required.
2025-07-31 14:14:37.2|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 11:25:04 and 07/31/2025 12:14:37 UTC. Search may be required.
2025-07-31 14:14:39.2|Info|DownloadDecisionMaker|Processing 752 releases
2025-07-31 14:15:42.5|Info|RssSyncService|RSS Sync Completed. Reports found: 752, Reports grabbed: 0
2025-07-31 14:17:30.1|Info|SceneMappingService|Updating Scene mappings
2025-07-31 14:31:00.2|Info|RssSyncService|Starting RSS Sync
2025-07-31 14:31:01.0|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 12:14:32 and 07/31/2025 12:14:32 UTC. Search may be required.
2025-07-31 14:31:01.1|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 12:14:32 and 07/29/2025 12:14:32 UTC. Search may be required.
2025-07-31 14:31:01.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 12:14:33 and 07/31/2025 12:14:33 UTC. Search may be required.
2025-07-31 14:31:01.1|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 12:14:32 and 07/29/2025 12:14:32 UTC. Search may be required.
2025-07-31 14:31:02.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 12:14:37 and 07/31/2025 12:14:37 UTC. Search may be required.
2025-07-31 14:31:02.8|Info|DownloadDecisionMaker|Processing 752 releases
2025-07-31 14:32:06.7|Info|RssSyncService|RSS Sync Completed. Reports found: 752, Reports grabbed: 0
2025-07-31 14:47:30.3|Info|RssSyncService|Starting RSS Sync
2025-07-31 14:47:31.3|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 12:14:32 and 07/29/2025 12:14:32 UTC. Search may be required.
2025-07-31 14:47:31.3|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 12:14:33 and 07/31/2025 12:14:33 UTC. Search may be required.
2025-07-31 14:47:31.5|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 12:14:32 and 07/29/2025 12:14:32 UTC. Search may be required.
2025-07-31 14:47:31.5|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 14:47:31.5|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 14:47:31.8|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 12:14:32 and 07/31/2025 12:14:32 UTC. Search may be required.
2025-07-31 14:47:32.5|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 12:14:37 and 07/31/2025 12:14:37 UTC. Search may be required.
2025-07-31 14:47:32.6|Info|DownloadDecisionMaker|Processing 652 releases
2025-07-31 14:48:26.5|Info|RssSyncService|RSS Sync Completed. Reports found: 652, Reports grabbed: 0
2025-07-31 15:03:30.4|Info|RssSyncService|Starting RSS Sync
2025-07-31 15:03:31.0|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 15:03:31.0|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 15:03:31.2|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 12:14:32 and 07/29/2025 13:03:31 UTC. Search may be required.
2025-07-31 15:03:31.2|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 12:14:32 and 07/29/2025 13:03:31 UTC. Search may be required.
2025-07-31 15:03:31.3|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 12:14:32 and 07/31/2025 13:03:31 UTC. Search may be required.
2025-07-31 15:03:32.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 12:14:33 and 07/31/2025 13:03:31 UTC. Search may be required.
2025-07-31 15:03:32.6|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/31/2025 12:13:33 and 07/31/2025 12:18:32 UTC. Search may be required.
2025-07-31 15:03:36.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 12:14:37 and 07/31/2025 13:03:36 UTC. Search may be required.
2025-07-31 15:03:37.5|Info|DownloadDecisionMaker|Processing 652 releases
2025-07-31 15:04:28.2|Info|RssSyncService|RSS Sync Completed. Reports found: 652, Reports grabbed: 0
2025-07-31 15:19:30.9|Info|RssSyncService|Starting RSS Sync
2025-07-31 15:19:31.3|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 13:03:31 and 07/29/2025 13:03:31 UTC. Search may be required.
2025-07-31 15:19:31.3|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 13:03:31 and 07/29/2025 13:03:31 UTC. Search may be required.
2025-07-31 15:19:31.6|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 13:03:31 and 07/31/2025 13:03:31 UTC. Search may be required.
2025-07-31 15:19:33.1|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 13:03:36 and 07/31/2025 13:03:36 UTC. Search may be required.
2025-07-31 15:19:33.1|Info|DownloadDecisionMaker|Processing 752 releases
2025-07-31 15:20:28.7|Info|RssSyncService|RSS Sync Completed. Reports found: 752, Reports grabbed: 0
2025-07-31 15:35:30.9|Info|RssSyncService|Starting RSS Sync
2025-07-31 15:35:31.0|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 13:03:31 and 07/29/2025 13:03:31 UTC. Search may be required.
2025-07-31 15:35:31.1|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 13:03:31 and 07/29/2025 13:03:31 UTC. Search may be required.
2025-07-31 15:35:31.2|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 13:03:31 and 07/31/2025 13:03:31 UTC. Search may be required.
2025-07-31 15:35:33.2|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 13:03:36 and 07/31/2025 13:03:36 UTC. Search may be required.
2025-07-31 15:35:33.2|Info|DownloadDecisionMaker|Processing 752 releases
2025-07-31 15:36:27.6|Info|RssSyncService|RSS Sync Completed. Reports found: 752, Reports grabbed: 0
2025-07-31 15:51:31.0|Info|RssSyncService|Starting RSS Sync
2025-07-31 15:51:31.9|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 13:03:31 and 07/29/2025 13:51:31 UTC. Search may be required.
2025-07-31 15:51:31.9|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 13:03:31 and 07/29/2025 13:51:31 UTC. Search may be required.
2025-07-31 15:51:32.0|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 13:03:31 and 07/31/2025 13:51:31 UTC. Search may be required.
2025-07-31 15:51:32.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 13:03:32 and 07/31/2025 13:51:32 UTC. Search may be required.
2025-07-31 15:51:35.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 13:03:36 and 07/31/2025 13:51:35 UTC. Search may be required.
2025-07-31 15:51:38.0|Info|DownloadDecisionMaker|Processing 755 releases
2025-07-31 15:52:34.4|Info|RssSyncService|RSS Sync Completed. Reports found: 755, Reports grabbed: 0
2025-07-31 16:08:01.1|Info|RssSyncService|Starting RSS Sync
2025-07-31 16:08:01.5|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 13:51:31 and 07/29/2025 13:51:31 UTC. Search may be required.
2025-07-31 16:08:01.5|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 13:51:31 and 07/29/2025 13:51:31 UTC. Search may be required.
2025-07-31 16:08:01.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 13:51:32 and 07/31/2025 13:51:32 UTC. Search may be required.
2025-07-31 16:08:02.0|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 13:51:31 and 07/31/2025 13:51:31 UTC. Search may be required.
2025-07-31 16:08:03.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 13:51:35 and 07/31/2025 13:51:35 UTC. Search may be required.
2025-07-31 16:08:03.4|Info|DownloadDecisionMaker|Processing 755 releases
2025-07-31 16:09:01.6|Info|RssSyncService|RSS Sync Completed. Reports found: 755, Reports grabbed: 0
2025-07-31 16:24:31.2|Info|RssSyncService|Starting RSS Sync
2025-07-31 16:24:31.9|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 13:51:31 and 07/29/2025 13:51:31 UTC. Search may be required.
2025-07-31 16:24:31.9|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 13:51:31 and 07/29/2025 13:51:31 UTC. Search may be required.
2025-07-31 16:24:31.9|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 13:51:32 and 07/31/2025 13:51:32 UTC. Search may be required.
2025-07-31 16:24:32.1|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 13:51:31 and 07/31/2025 13:51:31 UTC. Search may be required.
2025-07-31 16:24:33.4|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 13:51:35 and 07/31/2025 13:51:35 UTC. Search may be required.
2025-07-31 16:24:33.5|Info|DownloadDecisionMaker|Processing 755 releases
2025-07-31 16:25:29.9|Info|RssSyncService|RSS Sync Completed. Reports found: 755, Reports grabbed: 0
2025-07-31 16:40:31.2|Info|RssSyncService|Starting RSS Sync
2025-07-31 16:40:31.7|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 13:51:31 and 07/29/2025 14:40:31 UTC. Search may be required.
2025-07-31 16:40:31.7|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 13:51:31 and 07/29/2025 14:40:31 UTC. Search may be required.
2025-07-31 16:40:31.8|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 13:51:31 and 07/31/2025 14:40:31 UTC. Search may be required.
2025-07-31 16:40:32.2|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 13:51:32 and 07/31/2025 14:40:32 UTC. Search may be required.
2025-07-31 16:40:32.3|Warn|Torznab|Indexer TorrentGalaxyClone rss sync didn't cover the period between 07/31/2025 13:26:32 and 07/31/2025 14:25:31 UTC. Search may be required.
2025-07-31 16:40:36.2|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 13:51:35 and 07/31/2025 14:40:36 UTC. Search may be required.
2025-07-31 16:40:38.1|Info|DownloadDecisionMaker|Processing 755 releases
2025-07-31 16:41:42.3|Info|RssSyncService|RSS Sync Completed. Reports found: 755, Reports grabbed: 0
2025-07-31 16:57:01.3|Info|RssSyncService|Starting RSS Sync
2025-07-31 16:57:02.3|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 14:40:31 and 07/29/2025 14:40:31 UTC. Search may be required.
2025-07-31 16:57:02.3|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 14:40:31 and 07/29/2025 14:40:31 UTC. Search may be required.
2025-07-31 16:57:02.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 14:40:32 and 07/31/2025 14:40:32 UTC. Search may be required.
2025-07-31 16:57:02.8|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 14:40:31 and 07/31/2025 14:40:31 UTC. Search may be required.
2025-07-31 16:57:03.5|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 14:40:36 and 07/31/2025 14:40:36 UTC. Search may be required.
2025-07-31 16:57:03.5|Info|DownloadDecisionMaker|Processing 755 releases
2025-07-31 16:57:56.4|Info|RssSyncService|RSS Sync Completed. Reports found: 755, Reports grabbed: 0
2025-07-31 17:13:02.6|Info|RssSyncService|Starting RSS Sync
2025-07-31 17:13:05.5|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 14:40:31 and 07/29/2025 14:40:31 UTC. Search may be required.
2025-07-31 17:13:05.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 14:40:32 and 07/31/2025 14:40:32 UTC. Search may be required.
2025-07-31 17:13:05.6|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 14:40:31 and 07/31/2025 14:40:31 UTC. Search may be required.
2025-07-31 17:13:05.5|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 14:40:31 and 07/29/2025 14:40:31 UTC. Search may be required.
2025-07-31 17:13:05.8|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 14:40:36 and 07/31/2025 14:40:36 UTC. Search may be required.
2025-07-31 17:13:05.9|Info|DownloadDecisionMaker|Processing 755 releases
2025-07-31 17:14:11.6|Info|RssSyncService|RSS Sync Completed. Reports found: 755, Reports grabbed: 0
2025-07-31 17:17:32.6|Info|SceneMappingService|Updating Scene mappings
2025-07-31 17:29:32.7|Info|RssSyncService|Starting RSS Sync
2025-07-31 17:29:35.0|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 14:40:31 and 07/29/2025 15:29:34 UTC. Search may be required.
2025-07-31 17:29:35.0|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 14:40:31 and 07/29/2025 15:29:34 UTC. Search may be required.
2025-07-31 17:29:35.1|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 14:40:32 and 07/31/2025 15:29:35 UTC. Search may be required.
2025-07-31 17:29:35.3|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 14:40:31 and 07/31/2025 15:29:34 UTC. Search may be required.
2025-07-31 17:29:39.0|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 14:40:36 and 07/31/2025 15:29:39 UTC. Search may be required.
2025-07-31 17:29:41.5|Info|DownloadDecisionMaker|Processing 755 releases
2025-07-31 17:30:43.0|Info|RssSyncService|RSS Sync Completed. Reports found: 755, Reports grabbed: 0
2025-07-31 17:31:50.2|Info|AddSeriesService|Adding Series [360893][Chernobyl] Path: [/CONTENIDO/SERIES/Chernobyl]
2025-07-31 17:31:50.3|Info|RefreshSeriesService|Updating Chernobyl
2025-07-31 17:31:50.5|Info|RefreshEpisodeService|Starting episode info refresh for: [360893][Chernobyl]
2025-07-31 17:31:50.5|Info|RefreshEpisodeService|Finished episode refresh for series: [360893][Chernobyl].
2025-07-31 17:31:50.6|Info|MediaCoverService|Downloading Banner for [360893][Chernobyl] https://artworks.thetvdb.com/banners/v4/series/360893/banners/6751caac5a993.jpg
2025-07-31 17:31:50.7|Info|DiskScanService|Scanning Chernobyl
2025-07-31 17:31:50.8|Info|DiskScanService|Completed scanning disk for Chernobyl
2025-07-31 17:31:50.8|Info|SeriesScannedHandler|[Chernobyl] was recently added, performing post-add actions
2025-07-31 17:31:50.9|Info|MediaCoverService|Downloading Poster for [360893][Chernobyl] https://artworks.thetvdb.com/banners/posters/5cc12861c93e4.jpg
2025-07-31 17:31:51.1|Info|MediaCoverService|Downloading Fanart for [360893][Chernobyl] https://artworks.thetvdb.com/banners/series/360893/backgrounds/62017319.jpg
2025-07-31 17:31:51.2|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 17:31:51.2|Info|EpisodeSearchService|Performing search for 5 episodes
2025-07-31 17:31:51.2|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 17:31:51.3|Info|ReleaseSearchService|Searching indexers for [Chernobyl : S01]. 12 active indexers
2025-07-31 17:31:51.3|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 17:31:51.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 17:31:51.5|Info|MediaCoverService|Downloading Clearlogo for [360893][Chernobyl] https://artworks.thetvdb.com/banners/v4/series/360893/clearlogo/611be4d52d6a8.png
2025-07-31 17:31:52.9|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100&q=Chernobyl&season=1: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 17:31:53.0|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100&q=Chernobyl&season=1]
2025-07-31 17:31:55.8|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://192.168.18.10:8096/Items?recursive=true&includeItemTypes=Series&fields=Path%2CProviderIds&years=2019: 401.Unauthorized (0 bytes)
2025-07-31 17:31:55.8|Warn|NotificationService|Unable to process notification queue for Emby / Jellyfin

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [401:Unauthorized] [GET] at [http://192.168.18.10:8096/Items?recursive=true&includeItemTypes=Series&fields=Path%2CProviderIds&years=2019]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Common.Http.HttpClient.GetAsync[T](HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 336
   at NzbDrone.Common.Http.HttpClient.Get[T](HttpRequest request)
   at NzbDrone.Core.Notifications.Emby.MediaBrowserProxy.ProcessGetRequest[T](HttpRequest request, MediaBrowserSettings settings) in ./Sonarr.Core/Notifications/MediaBrowser/MediaBrowserProxy.cs:line 150
   at NzbDrone.Core.Notifications.Emby.MediaBrowserProxy.GetPaths(MediaBrowserSettings settings, Series series) in ./Sonarr.Core/Notifications/MediaBrowser/MediaBrowserProxy.cs:line 70
   at NzbDrone.Core.Notifications.Emby.MediaBrowserService.Update(MediaBrowserSettings settings, Series series, String updateType) in ./Sonarr.Core/Notifications/MediaBrowser/MediaBrowserService.cs:line 43
   at NzbDrone.Core.Notifications.Emby.MediaBrowser.<ProcessQueue>b__21_1(UpdateQueueItem`1 item) in ./Sonarr.Core/Notifications/MediaBrowser/MediaBrowser.cs:line 132
   at System.Collections.Generic.List`1.ForEach(Action`1 action)
   at NzbDrone.Core.Notifications.Emby.MediaBrowser.<ProcessQueue>b__21_0(List`1 items) in ./Sonarr.Core/Notifications/MediaBrowser/MediaBrowser.cs:line 127
   at NzbDrone.Core.Notifications.MediaServerUpdateQueue`2.ProcessQueue(String identifier, Action`1 update) in ./Sonarr.Core/Notifications/MediaServerUpdateQueue.cs:line 79
   at NzbDrone.Core.Notifications.NotificationService.ProcessQueue() in ./Sonarr.Core/Notifications/NotificationService.cs:line 559


2025-07-31 17:31:56.0|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/1337x/results/torznab/api?t=tvsearch&cat=5000,5030,5040,5070,5080,8000,8010,100001,100002,100003,100004,100005,100006,100007,100009,100010,100011,100012,100013,100014,100015,100016,100017,100018,100019,100020,100021,100022,100023,100024,100025,100026,100027,100028,100033,100034,100035,100036,100037,100038,100039,100040,100041,100042,100043,100044,100045,100046,100047,100048,100049,100050,100051,100052,100053,100054,100055,100056,100057,100058,100059,100060,100066,100067,100068,100069,100070,100071,100072,100073,100074,100075,100076,100077,100078,100079,100080,100081,100082,2000,2010,2030,2040,2045,2060,2070&extended=1&apikey=(removed)&offset=0&limit=100&q=Chernobyl&season=1: 400.BadRequest (7379 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (1337x): Error connecting to FlareSolverr server: System.Net.Http.HttpRequestException: Name does not resolve (flaresolver:8191)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA; ---&gt; FlareSolverrSharp.Exceptions.FlareSolverrException: Error connecting to FlareSolverr server: System.Net.Http.HttpRequestException: Name does not resolve (flaresolver:8191)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA;--- End of stack trace from previous location ---&#xA;   at FlareSolverrSharp.Utilities.SemaphoreLocker.LockAsync[T](Func`1 worker)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.SendFlareSolverrRequest(HttpContent flareSolverrRequest)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.Solve(HttpRequestMessage request, String sessionId)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 17:31:56.0|Warn|Torznab|1337x HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/1337x/results/torznab/api?t=tvsearch&cat=5000,5030,5040,5070,5080,8000,8010,100001,100002,100003,100004,100005,100006,100007,100009,100010,100011,100012,100013,100014,100015,100016,100017,100018,100019,100020,100021,100022,100023,100024,100025,100026,100027,100028,100033,100034,100035,100036,100037,100038,100039,100040,100041,100042,100043,100044,100045,100046,100047,100048,100049,100050,100051,100052,100053,100054,100055,100056,100057,100058,100059,100060,100066,100067,100068,100069,100070,100071,100072,100073,100074,100075,100076,100077,100078,100079,100080,100081,100082,2000,2010,2030,2040,2045,2060,2070&extended=1&apikey=(removed)&offset=0&limit=100&q=Chernobyl&season=1]
2025-07-31 17:31:59.9|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100&q=Chernobyl&season=1: 400.BadRequest (7386 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Error connecting to FlareSolverr server: System.Net.Http.HttpRequestException: Name does not resolve (flaresolver:8191)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA; ---&gt; FlareSolverrSharp.Exceptions.FlareSolverrException: Error connecting to FlareSolverr server: System.Net.Http.HttpRequestException: Name does not resolve (flaresolver:8191)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA;--- End of stack trace from previous location ---&#xA;   at FlareSolverrSharp.Utilities.SemaphoreLocker.LockAsync[T](Func`1 worker)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.SendFlareSolverrRequest(HttpContent flareSolverrRequest)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.Solve(HttpRequestMessage request, String sessionId)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 17:32:00.0|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100&q=Chernobyl&season=1]
2025-07-31 17:32:00.7|Info|DownloadDecisionMaker|Processing 172 releases
2025-07-31 17:32:55.8|Info|DownloadService|Report sent to qBittorrent. Indexer Elitetorrent-wf. Chernobyl S01E05 1080p LATiN SPANiSH
2025-07-31 17:32:56.0|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [POST] http://192.168.18.10:8082/webhook/sonarr: 404.NotFound (22 bytes)
{"detail":"Not Found"}
2025-07-31 17:32:56.0|Error|NotificationService|Unable to send OnGrab notification to Telegram Bot

[v4.0.15.2941] NzbDrone.Core.Notifications.Webhook.WebhookException: Unable to post to webhook: HTTP request failed: [404:NotFound] [POST] at [http://192.168.18.10:8082/webhook/sonarr]
 ---> NzbDrone.Common.Http.HttpException: HTTP request failed: [404:NotFound] [POST] at [http://192.168.18.10:8082/webhook/sonarr]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Common.Http.HttpClient.Execute(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 128
   at NzbDrone.Core.Notifications.Webhook.WebhookProxy.SendWebhook(WebhookPayload body, WebhookSettings settings) in ./Sonarr.Core/Notifications/Webhook/WebhookProxy.cs:line 51
{"detail":"Not Found"}
   --- End of inner exception stack trace ---
   at NzbDrone.Core.Notifications.Webhook.WebhookProxy.SendWebhook(WebhookPayload body, WebhookSettings settings) in ./Sonarr.Core/Notifications/Webhook/WebhookProxy.cs:line 55
   at NzbDrone.Core.Notifications.Webhook.Webhook.OnGrab(GrabMessage message) in ./Sonarr.Core/Notifications/Webhook/Webhook.cs:line 28
   at NzbDrone.Core.Notifications.NotificationService.Handle(EpisodeGrabbedEvent message) in ./Sonarr.Core/Notifications/NotificationService.cs:line 160

2025-07-31 17:32:56.2|Info|EpisodeSearchService|Completed search for 5 episodes. 1 reports downloaded.
2025-07-31 17:37:00.0|Info|ReleaseSearchService|Searching indexers for [Chernobyl : S01E04]. 12 active indexers
2025-07-31 17:37:01.1|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100&q=Chernobyl&season=1&ep=4: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 17:37:01.1|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100&q=Chernobyl&season=1&ep=4: 400.BadRequest (3989 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (ilcorsaronero): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (ilcorsaronero.link:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 17:37:01.1|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100&q=Chernobyl&season=1&ep=4]
2025-07-31 17:37:01.2|Warn|Torznab|Ilcorsaronero HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/ilcorsaronero/results/torznab/api?t=tvsearch&cat=5000,5070,8000,101640,103227,105369,111386,113997,114721,117130,118333,118875,125683,125856,125992,126360,133225,137068,137795,140001,141522,145471,148013,149682,150195,151874,151991,152890,154513,2000&extended=1&apikey=(removed)&offset=0&limit=100&q=Chernobyl&season=1&ep=4]
2025-07-31 17:37:05.2|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/1337x/results/torznab/api?t=tvsearch&cat=5000,5030,5040,5070,5080,8000,8010,100001,100002,100003,100004,100005,100006,100007,100009,100010,100011,100012,100013,100014,100015,100016,100017,100018,100019,100020,100021,100022,100023,100024,100025,100026,100027,100028,100033,100034,100035,100036,100037,100038,100039,100040,100041,100042,100043,100044,100045,100046,100047,100048,100049,100050,100051,100052,100053,100054,100055,100056,100057,100058,100059,100060,100066,100067,100068,100069,100070,100071,100072,100073,100074,100075,100076,100077,100078,100079,100080,100081,100082,2000,2010,2030,2040,2045,2060,2070&extended=1&apikey=(removed)&offset=0&limit=100&q=Chernobyl&season=1&ep=4: 400.BadRequest (7379 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (1337x): Error connecting to FlareSolverr server: System.Net.Http.HttpRequestException: Name does not resolve (flaresolver:8191)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA; ---&gt; FlareSolverrSharp.Exceptions.FlareSolverrException: Error connecting to FlareSolverr server: System.Net.Http.HttpRequestException: Name does not resolve (flaresolver:8191)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.RedirectHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.&lt;&gt;c__DisplayClass12_0.&lt;&lt;SendFlareSolverrRequest&gt;b__0&gt;d.MoveNext()&#xA;--- End of stack trace from previous location ---&#xA;   at FlareSolverrSharp.Utilities.SemaphoreLocker.LockAsync[T](Func`1 worker)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.SendFlareSolverrRequest(HttpContent flareSolverrRequest)&#xA;   at FlareSolverrSharp.Solvers.FlareSolverr.Solve(HttpRequestMessage request, String sessionId)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 17:37:05.3|Warn|Torznab|1337x HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/1337x/results/torznab/api?t=tvsearch&cat=5000,5030,5040,5070,5080,8000,8010,100001,100002,100003,100004,100005,100006,100007,100009,100010,100011,100012,100013,100014,100015,100016,100017,100018,100019,100020,100021,100022,100023,100024,100025,100026,100027,100028,100033,100034,100035,100036,100037,100038,100039,100040,100041,100042,100043,100044,100045,100046,100047,100048,100049,100050,100051,100052,100053,100054,100055,100056,100057,100058,100059,100060,100066,100067,100068,100069,100070,100071,100072,100073,100074,100075,100076,100077,100078,100079,100080,100081,100082,2000,2010,2030,2040,2045,2060,2070&extended=1&apikey=(removed)&offset=0&limit=100&q=Chernobyl&season=1&ep=4]
2025-07-31 17:37:05.5|Info|DownloadDecisionMaker|Processing 100 releases
2025-07-31 17:38:03.0|Info|RecycleBinProvider|Attempting to send '/CONTENIDO/SERIES/Chernobyl' to recycling bin
2025-07-31 17:38:03.1|Info|RecycleBinProvider|Recycling Bin has not been configured, deleting permanently. /CONTENIDO/SERIES/Chernobyl
2025-07-31 17:38:03.2|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://192.168.18.10:8096/Items?recursive=true&includeItemTypes=Series&fields=Path%2CProviderIds&years=2019: 401.Unauthorized (0 bytes)
2025-07-31 17:38:03.3|Warn|NotificationService|Unable to process notification queue for Emby / Jellyfin

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [401:Unauthorized] [GET] at [http://192.168.18.10:8096/Items?recursive=true&includeItemTypes=Series&fields=Path%2CProviderIds&years=2019]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Common.Http.HttpClient.GetAsync[T](HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 336
   at NzbDrone.Common.Http.HttpClient.Get[T](HttpRequest request)
   at NzbDrone.Core.Notifications.Emby.MediaBrowserProxy.ProcessGetRequest[T](HttpRequest request, MediaBrowserSettings settings) in ./Sonarr.Core/Notifications/MediaBrowser/MediaBrowserProxy.cs:line 150
   at NzbDrone.Core.Notifications.Emby.MediaBrowserProxy.GetPaths(MediaBrowserSettings settings, Series series) in ./Sonarr.Core/Notifications/MediaBrowser/MediaBrowserProxy.cs:line 70
   at NzbDrone.Core.Notifications.Emby.MediaBrowserService.Update(MediaBrowserSettings settings, Series series, String updateType) in ./Sonarr.Core/Notifications/MediaBrowser/MediaBrowserService.cs:line 43
   at NzbDrone.Core.Notifications.Emby.MediaBrowser.<ProcessQueue>b__21_1(UpdateQueueItem`1 item) in ./Sonarr.Core/Notifications/MediaBrowser/MediaBrowser.cs:line 132
   at System.Collections.Generic.List`1.ForEach(Action`1 action)
   at NzbDrone.Core.Notifications.Emby.MediaBrowser.<ProcessQueue>b__21_0(List`1 items) in ./Sonarr.Core/Notifications/MediaBrowser/MediaBrowser.cs:line 127
   at NzbDrone.Core.Notifications.MediaServerUpdateQueue`2.ProcessQueue(String identifier, Action`1 update) in ./Sonarr.Core/Notifications/MediaServerUpdateQueue.cs:line 79
   at NzbDrone.Core.Notifications.NotificationService.ProcessQueue() in ./Sonarr.Core/Notifications/NotificationService.cs:line 559


2025-07-31 17:38:17.2|Info|AddSeriesService|Adding Series [360893][Chernobyl] Path: [/CONTENIDO/SERIES/Chernobyl]
2025-07-31 17:38:17.3|Info|RefreshSeriesService|Updating Chernobyl
2025-07-31 17:38:17.4|Info|RefreshEpisodeService|Starting episode info refresh for: [360893][Chernobyl]
2025-07-31 17:38:17.4|Info|RefreshEpisodeService|Finished episode refresh for series: [360893][Chernobyl].
2025-07-31 17:38:17.5|Info|MediaCoverService|Downloading Banner for [360893][Chernobyl] https://artworks.thetvdb.com/banners/v4/series/360893/banners/6751caac5a993.jpg
2025-07-31 17:38:17.6|Info|DiskScanService|Scanning Chernobyl
2025-07-31 17:38:17.6|Warn|MediaCoverMapper|File /config/MediaCover/69/poster.jpg not found
2025-07-31 17:38:17.7|Info|DiskScanService|Completed scanning disk for Chernobyl
2025-07-31 17:38:17.7|Info|SeriesScannedHandler|[Chernobyl] was recently added, performing post-add actions
2025-07-31 17:38:17.9|Info|MediaCoverService|Downloading Poster for [360893][Chernobyl] https://artworks.thetvdb.com/banners/posters/5cc12861c93e4.jpg
2025-07-31 17:38:18.0|Info|EpisodeSearchService|Performing search for 5 episodes
2025-07-31 17:38:18.0|Info|ExistingMetadataImporter|Found 0 existing metadata files
2025-07-31 17:38:18.1|Info|ExistingSubtitleImporter|Found 0 existing subtitle files
2025-07-31 17:38:18.1|Info|ReleaseSearchService|Searching indexers for [Chernobyl : S01]. 9 active indexers
2025-07-31 17:38:18.2|Info|MediaCoverService|Downloading Fanart for [360893][Chernobyl] https://artworks.thetvdb.com/banners/series/360893/backgrounds/62017319.jpg
2025-07-31 17:38:18.2|Info|ExistingOtherExtraImporter|Found 0 existing other extra files
2025-07-31 17:38:18.3|Info|ExistingExtraFileService|Found 0 possible extra files, imported 0 files.
2025-07-31 17:38:18.5|Info|MediaCoverService|Downloading Clearlogo for [360893][Chernobyl] https://artworks.thetvdb.com/banners/v4/series/360893/clearlogo/611be4d52d6a8.png
2025-07-31 17:38:22.3|Info|DownloadDecisionMaker|Processing 172 releases
2025-07-31 17:38:22.7|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://192.168.18.10:8096/Items?recursive=true&includeItemTypes=Series&fields=Path%2CProviderIds&years=2019: 401.Unauthorized (0 bytes)
2025-07-31 17:38:22.7|Warn|NotificationService|Unable to process notification queue for Emby / Jellyfin

[v4.0.15.2941] NzbDrone.Common.Http.HttpException: HTTP request failed: [401:Unauthorized] [GET] at [http://192.168.18.10:8096/Items?recursive=true&includeItemTypes=Series&fields=Path%2CProviderIds&years=2019]
   at NzbDrone.Common.Http.HttpClient.ExecuteAsync(HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 119
   at NzbDrone.Common.Http.HttpClient.GetAsync[T](HttpRequest request) in ./Sonarr.Common/Http/HttpClient.cs:line 336
   at NzbDrone.Common.Http.HttpClient.Get[T](HttpRequest request)
   at NzbDrone.Core.Notifications.Emby.MediaBrowserProxy.ProcessGetRequest[T](HttpRequest request, MediaBrowserSettings settings) in ./Sonarr.Core/Notifications/MediaBrowser/MediaBrowserProxy.cs:line 150
   at NzbDrone.Core.Notifications.Emby.MediaBrowserProxy.GetPaths(MediaBrowserSettings settings, Series series) in ./Sonarr.Core/Notifications/MediaBrowser/MediaBrowserProxy.cs:line 70
   at NzbDrone.Core.Notifications.Emby.MediaBrowserService.Update(MediaBrowserSettings settings, Series series, String updateType) in ./Sonarr.Core/Notifications/MediaBrowser/MediaBrowserService.cs:line 43
   at NzbDrone.Core.Notifications.Emby.MediaBrowser.<ProcessQueue>b__21_1(UpdateQueueItem`1 item) in ./Sonarr.Core/Notifications/MediaBrowser/MediaBrowser.cs:line 132
   at System.Collections.Generic.List`1.ForEach(Action`1 action)
   at NzbDrone.Core.Notifications.Emby.MediaBrowser.<ProcessQueue>b__21_0(List`1 items) in ./Sonarr.Core/Notifications/MediaBrowser/MediaBrowser.cs:line 127
   at NzbDrone.Core.Notifications.MediaServerUpdateQueue`2.ProcessQueue(String identifier, Action`1 update) in ./Sonarr.Core/Notifications/MediaServerUpdateQueue.cs:line 79
   at NzbDrone.Core.Notifications.NotificationService.ProcessQueue() in ./Sonarr.Core/Notifications/NotificationService.cs:line 559


2025-07-31 17:39:06.4|Warn|ProcessDownloadDecisions|Couldn't add release 'Chernobyl S01E05 1080p LATiN SPANiSH' from Indexer Elitetorrent-wf to download queue.

[v4.0.15.2941] NzbDrone.Core.Download.Clients.DownloadClientException: Download client failed to add torrent by url
   at NzbDrone.Core.Download.Clients.QBittorrent.QBittorrentProxyV2.AddTorrentFromUrl(String torrentUrl, TorrentSeedConfiguration seedConfiguration, QBittorrentSettings settings) in ./Sonarr.Core/Download/Clients/QBittorrent/QBittorrentProxyV2.cs:line 164
   at NzbDrone.Core.Download.Clients.QBittorrent.QBittorrent.AddFromMagnetLink(RemoteEpisode remoteEpisode, String hash, String magnetLink) in ./Sonarr.Core/Download/Clients/QBittorrent/QBittorrent.cs:line 84
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromMagnetUrl(RemoteEpisode remoteEpisode, IIndexer indexer, String magnetUrl) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 235
   at NzbDrone.Core.Download.TorrentClientBase`1.DownloadFromWebUrl(RemoteEpisode remoteEpisode, IIndexer indexer, String torrentUrl) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 158
   at NzbDrone.Core.Download.TorrentClientBase`1.Download(RemoteEpisode remoteEpisode, IIndexer indexer) in ./Sonarr.Core/Download/TorrentClientBase.cs:line 124
   at NzbDrone.Core.Download.DownloadService.DownloadReport(RemoteEpisode remoteEpisode, IDownloadClient downloadClient) in ./Sonarr.Core/Download/DownloadService.cs:line 98
   at NzbDrone.Core.Download.DownloadService.DownloadReport(RemoteEpisode remoteEpisode, Nullable`1 downloadClientId) in ./Sonarr.Core/Download/DownloadService.cs:line 63
   at NzbDrone.Core.Download.ProcessDownloadDecisions.ProcessDecisionInternal(DownloadDecision decision, Nullable`1 downloadClientId) in ./Sonarr.Core/Download/ProcessDownloadDecisions.cs:line 204

2025-07-31 17:39:06.4|Info|EpisodeSearchService|Completed search for 5 episodes. 0 reports downloaded.
2025-07-31 17:46:02.7|Info|RssSyncService|Starting RSS Sync
2025-07-31 17:46:03.3|Warn|HttpClient|HTTP Error - Res: HTTP/1.1 [GET] http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100: 400.BadRequest (3990 bytes)
<?xml version="1.0" encoding="UTF-8"?>
<error code="900" description="Jackett.Common.IndexerException: Exception (limetorrents): Name does not resolve&#xA; ---&gt; System.Net.Http.HttpRequestException: Name does not resolve (www.limetorrents.lol:443)&#xA; ---&gt; System.Net.Sockets.SocketException (0xFFFDFFFF): Name does not resolve&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.ThrowException(SocketError error, CancellationToken cancellationToken)&#xA;   at System.Net.Sockets.Socket.AwaitableSocketAsyncEventArgs.System.Threading.Tasks.Sources.IValueTaskSource.GetResult(Int16 token)&#xA;   at System.Net.Sockets.Socket.&lt;ConnectAsync&gt;g__WaitForConnectWithCancellation|285_0(AwaitableSocketAsyncEventArgs saea, ValueTask connectTask, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   --- End of inner exception stack trace ---&#xA;   at System.Net.Http.HttpConnectionPool.ConnectToTcpHostAsync(String host, Int32 port, HttpRequestMessage initialRequest, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.ConnectAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.CreateHttp11ConnectionAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.AddHttp11ConnectionAsync(QueueItem queueItem)&#xA;   at System.Threading.Tasks.TaskCompletionSourceWithCancellation`1.WaitWithCancellationAsync(CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpConnectionPool.SendWithVersionDetectionAndRetryAsync(HttpRequestMessage request, Boolean async, Boolean doRequestAuth, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DiagnosticsHandler.SendAsyncCore(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at System.Net.Http.DecompressionHandler.SendAsync(HttpRequestMessage request, Boolean async, CancellationToken cancellationToken)&#xA;   at FlareSolverrSharp.ClearanceHandler.SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)&#xA;   at System.Net.Http.HttpClient.&lt;SendAsync&gt;g__Core|83_0(HttpRequestMessage request, HttpCompletionOption completionOption, CancellationTokenSource cts, Boolean disposeCts, CancellationTokenSource pendingRequestsCts, CancellationToken originalCancellationToken)&#xA;   at Jackett.Common.Utils.Clients.HttpWebClient2.Run(WebRequest webRequest) in ./Jackett.Common/Utils/Clients/HttpWebClient2.cs:line 180&#xA;   at Jackett.Common.Utils.Clients.WebClient.GetResultAsync(WebRequest request) in ./Jackett.Common/Utils/Clients/WebClient.cs:line 188&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.RequestWithCookiesAsync(String url, String cookieOverride, RequestType method, String referer, IEnumerable`1 data, Dictionary`2 headers, String rawbody, Nullable`1 emulateBrowser) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 617&#xA;   at Jackett.Common.Indexers.Definitions.CardigannIndexer.PerformQuery(TorznabQuery query) in ./Jackett.Common/Indexers/Definitions/CardigannIndexer.cs:line 1563&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 378&#xA;   --- End of inner exception stack trace ---&#xA;   at Jackett.Common.Indexers.BaseIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 403&#xA;   at Jackett.Common.Indexers.BaseWebIndexer.ResultsForQuery(TorznabQuery query, Boolean isMetaIndexer) in ./Jackett.Common/Indexers/BaseIndexer.cs:line 816&#xA;   at Jackett.Server.Controllers.ResultsController.Torznab(TorznabRequest request) in ./Jackett.Server/Controllers/ResultsController.cs:line 437" />
2025-07-31 17:46:03.3|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 15:29:35 and 07/29/2025 15:46:03 UTC. Search may be required.
2025-07-31 17:46:03.3|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 15:29:34 and 07/29/2025 15:46:03 UTC. Search may be required.
2025-07-31 17:46:03.4|Warn|Torznab|LimeTorrents HTTP request failed: [400:BadRequest] [GET] at [http://jackett:9117/api/v2.0/indexers/limetorrents/results/torznab/api?t=tvsearch&cat=5000,5070,8000,100467,104627,112972,121527,127246,136409,146065,151062,2000&extended=1&apikey=(removed)&offset=0&limit=100]
2025-07-31 17:46:03.4|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 15:29:34 and 07/31/2025 15:46:03 UTC. Search may be required.
2025-07-31 17:46:03.9|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 15:29:35 and 07/31/2025 15:46:03 UTC. Search may be required.
2025-07-31 17:46:07.7|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 15:29:39 and 07/31/2025 15:46:07 UTC. Search may be required.
2025-07-31 17:46:11.4|Info|DownloadDecisionMaker|Processing 655 releases
2025-07-31 17:47:13.9|Info|RssSyncService|RSS Sync Completed. Reports found: 655, Reports grabbed: 0
2025-07-31 17:50:18.7|Info|Bootstrap|Starting Sonarr - /app/sonarr/bin/Sonarr - Version 4.0.15.2941
2025-07-31 17:50:19.5|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-07-31 17:50:20.1|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-07-31 17:50:23.6|Info|AppFolderInfo|Data directory is being overridden to [/config]
2025-07-31 17:50:24.9|Info|MigrationController|*** Migrating data source=/config/sonarr.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-07-31 17:50:25.6|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-07-31 17:50:25.7|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-07-31 17:50:25.7|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-07-31 17:50:26.1|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-07-31 17:50:26.2|Info|FluentMigrator.Runner.MigrationRunner|=> 0.4198942s
2025-07-31 17:50:26.2|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-07-31 17:50:26.2|Info|FluentMigrator.Runner.MigrationRunner|=> 0.4383448s
2025-07-31 17:50:26.5|Info|MigrationController|*** Migrating data source=/config/logs.db;cache size=-20000;datetimekind=Utc;journal mode=Wal;pooling=True;version=3;busytimeout=100 ***
2025-07-31 17:50:26.8|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrating
2025-07-31 17:50:26.8|Info|FluentMigrator.Runner.MigrationRunner|PerformDBOperation 
2025-07-31 17:50:26.8|Info|NzbDrone.Core.Datastore.Migration.Framework.NzbDroneSQLiteProcessor|Performing DB Operation
2025-07-31 17:50:26.9|Info|DatabaseEngineVersionCheck|SQLite 3.49.2
2025-07-31 17:50:26.9|Info|FluentMigrator.Runner.MigrationRunner|=> 0.092977s
2025-07-31 17:50:26.9|Info|FluentMigrator.Runner.MigrationRunner|DatabaseEngineVersionCheck migrated
2025-07-31 17:50:27.0|Info|FluentMigrator.Runner.MigrationRunner|=> 0.1293238s
2025-07-31 17:50:33.6|Info|Microsoft.Hosting.Lifetime|Now listening on: http://[::]:8989
2025-07-31 17:50:39.0|Info|Microsoft.Hosting.Lifetime|Application started. Press Ctrl+C to shut down.
2025-07-31 17:50:39.1|Info|Microsoft.Hosting.Lifetime|Hosting environment: Production
2025-07-31 17:50:39.2|Info|Microsoft.Hosting.Lifetime|Content root path: /app/sonarr/bin
2025-07-31 17:50:39.8|Info|ManagedHttpDispatcher|IPv4 is available: True, IPv6 will be disabled
2025-07-31 18:02:38.5|Info|RssSyncService|Starting RSS Sync
2025-07-31 18:02:39.8|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 15:46:03 and 07/31/2025 16:02:39 UTC. Search may be required.
2025-07-31 18:02:40.4|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 15:46:03 and 07/29/2025 16:02:40 UTC. Search may be required.
2025-07-31 18:02:40.4|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 15:46:03 and 07/29/2025 16:02:40 UTC. Search may be required.
2025-07-31 18:02:40.5|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 15:46:03 and 07/31/2025 16:02:40 UTC. Search may be required.
2025-07-31 18:02:42.4|Warn|Torznab|Indexer LimeTorrents rss sync didn't cover the period between 07/31/2025 14:43:03 and 07/31/2025 15:02:40 UTC. Search may be required.
2025-07-31 18:02:45.5|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 15:46:07 and 07/31/2025 16:02:45 UTC. Search may be required.
2025-07-31 18:02:46.3|Info|DownloadDecisionMaker|Processing 755 releases
2025-07-31 18:03:45.5|Info|RssSyncService|RSS Sync Completed. Reports found: 755, Reports grabbed: 0
2025-07-31 18:19:08.6|Info|RssSyncService|Starting RSS Sync
2025-07-31 18:19:08.9|Warn|Torznab|Indexer DivxTotal rss sync didn't cover the period between 07/29/2025 16:02:40 and 07/29/2025 16:02:40 UTC. Search may be required.
2025-07-31 18:19:08.9|Warn|Torznab|Indexer BitSearch rss sync didn't cover the period between 07/29/2025 16:02:40 and 07/29/2025 16:02:40 UTC. Search may be required.
2025-07-31 18:19:08.9|Warn|Torznab|Indexer Elitetorrent-wf rss sync didn't cover the period between 07/31/2025 16:02:40 and 07/31/2025 16:02:40 UTC. Search may be required.
2025-07-31 18:19:09.0|Warn|TorrentRssIndexer|Indexer 0Magnet rss sync didn't cover the period between 07/31/2025 16:02:39 and 07/31/2025 16:02:39 UTC. Search may be required.
2025-07-31 18:19:10.9|Warn|Torznab|Indexer Wolfmax rss sync didn't cover the period between 07/31/2025 16:02:45 and 07/31/2025 16:02:45 UTC. Search may be required.
2025-07-31 18:19:10.9|Info|DownloadDecisionMaker|Processing 755 releases
2025-07-31 18:20:27.3|Info|RssSyncService|RSS Sync Completed. Reports found: 755, Reports grabbed: 0
