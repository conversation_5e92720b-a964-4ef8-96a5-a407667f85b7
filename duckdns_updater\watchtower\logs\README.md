# Watchtower Logs

Este directorio contiene los logs de Watchtower, el servicio de actualización automática de contenedores Docker.

## Configuración de Logging

### Logs de Aplicación
- **Ubicación**: `C:\docker\duckdns_updater\watchtower\logs`
- **Formato**: JSON para facilitar el análisis
- **Nivel**: INFO con DEBUG habilitado

### Logs de Docker
- **Driver**: json-file
- **Rotación automática**: 
  - Tamaño máximo por archivo: 10MB
  - Número máximo de archivos: 5
  - Compresión: Habilitada

## Archivos de Log

- Los logs de la aplicación Watchtower se escribirán en este directorio
- Los logs del contenedor Docker se gestionan automáticamente con rotación
- Los archivos antiguos se comprimen automáticamente para ahorrar espacio

## Monitoreo

Para ver los logs en tiempo real:

```bash
# Logs del contenedor Docker
docker logs -f watchtower

# Logs de aplicación (si Watchtower los escribe en archivos)
tail -f C:\docker\duckdns_updater\watchtower\logs\watchtower.log
```

## Limpieza

Los logs se rotan automáticamente, pero puedes limpiar manualmente si es necesario:
- Los logs de Docker se limpian automáticamente según la configuración
- Los logs de aplicación en este directorio pueden requerir limpieza manual ocasional
